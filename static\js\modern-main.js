// Modern JavaScript for Uproar Guild Management

class UproarApp {
    constructor() {
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initializeComponents();
        this.setupAnimations();
    }

    setupEventListeners() {
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Enhanced table sorting
        this.setupTableSorting();
        
        // Search functionality
        this.setupSearch();
        
        // Tooltips
        this.initializeTooltips();
    }

    setupTableSorting() {
        const tables = document.querySelectorAll('.table-modern');
        tables.forEach(table => {
            const headers = table.querySelectorAll('th[data-sort]');
            headers.forEach(header => {
                header.style.cursor = 'pointer';
                header.addEventListener('click', () => {
                    this.sortTable(table, header.dataset.sort, header);
                });
            });
        });
    }

    sortTable(table, column, header) {
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));
        const isAscending = !header.classList.contains('sort-asc');
        
        // Remove sort classes from all headers
        table.querySelectorAll('th').forEach(th => {
            th.classList.remove('sort-asc', 'sort-desc');
        });
        
        // Add appropriate class to current header
        header.classList.add(isAscending ? 'sort-asc' : 'sort-desc');
        
        // Sort rows
        rows.sort((a, b) => {
            const aValue = this.getCellValue(a, column);
            const bValue = this.getCellValue(b, column);
            
            if (column === 'ilvl') {
                return isAscending ? 
                    parseInt(aValue) - parseInt(bValue) : 
                    parseInt(bValue) - parseInt(aValue);
            }
            
            return isAscending ? 
                aValue.localeCompare(bValue) : 
                bValue.localeCompare(aValue);
        });
        
        // Reorder rows in DOM
        rows.forEach(row => tbody.appendChild(row));
        
        // Update counters if function exists
        if (typeof updateCounters === 'function') {
            updateCounters();
        }
    }

    getCellValue(row, column) {
        const columnMap = {
            'character-name': 0,
            'realm': 1,
            'ilvl': 2,
            'role': 3,
            'class': 4,
            'spec': 5,
            'armor': 6,
            'tier': 7
        };
        
        const cellIndex = columnMap[column];
        const cell = row.cells[cellIndex];
        return cell ? cell.textContent.trim() : '';
    }

    setupSearch() {
        const searchInputs = document.querySelectorAll('.search-input');
        searchInputs.forEach(input => {
            input.addEventListener('input', (e) => {
                this.filterTable(e.target.value, e.target.dataset.target);
            });
        });
    }

    filterTable(searchTerm, targetTable) {
        const table = document.querySelector(targetTable);
        if (!table) return;
        
        const rows = table.querySelectorAll('tbody tr');
        const term = searchTerm.toLowerCase();
        
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(term) ? '' : 'none';
        });
        
        // Update counters if function exists
        if (typeof updateCounters === 'function') {
            updateCounters();
        }
    }

    initializeTooltips() {
        // Initialize Bootstrap tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    setupAnimations() {
        // Intersection Observer for fade-in animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('fade-in-up');
                    observer.unobserve(entry.target);
                }
            });
        }, observerOptions);

        // Observe elements for animation
        document.querySelectorAll('.card, .stats-card, .table-modern').forEach(el => {
            observer.observe(el);
        });
    }

    initializeComponents() {
        // Initialize any custom components
        this.initializeCharts();
        this.setupRefreshButton();
    }

    initializeCharts() {
        // Role distribution chart
        const roleChartCanvas = document.getElementById('roleChart');
        if (roleChartCanvas) {
            this.createRoleChart(roleChartCanvas);
        }

        // Class distribution chart
        const classChartCanvas = document.getElementById('classChart');
        if (classChartCanvas) {
            this.createClassChart(classChartCanvas);
        }
    }

    createRoleChart(canvas) {
        const ctx = canvas.getContext('2d');
        
        // Get role data from the page
        const roleData = this.getRoleData();
        
        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Tanks', 'Healers', 'Melee DPS', 'Ranged DPS'],
                datasets: [{
                    data: roleData,
                    backgroundColor: [
                        'rgba(199, 156, 110, 0.8)',
                        'rgba(0, 255, 150, 0.8)',
                        'rgba(196, 31, 59, 0.8)',
                        'rgba(105, 204, 240, 0.8)'
                    ],
                    borderColor: [
                        'rgba(199, 156, 110, 1)',
                        'rgba(0, 255, 150, 1)',
                        'rgba(196, 31, 59, 1)',
                        'rgba(105, 204, 240, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            color: '#f0f6fc',
                            padding: 20
                        }
                    }
                }
            }
        });
    }

    createClassChart(canvas) {
        const ctx = canvas.getContext('2d');
        
        // Get class data from the page
        const classData = this.getClassData();
        
        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: classData.labels,
                datasets: [{
                    label: 'Characters',
                    data: classData.values,
                    backgroundColor: classData.colors,
                    borderColor: classData.colors,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            color: '#8b949e'
                        },
                        grid: {
                            color: 'rgba(139, 148, 158, 0.1)'
                        }
                    },
                    x: {
                        ticks: {
                            color: '#8b949e'
                        },
                        grid: {
                            color: 'rgba(139, 148, 158, 0.1)'
                        }
                    }
                }
            }
        });
    }

    getRoleData() {
        // Extract role data from counters or table
        const roles = ['Tank', 'Healer', 'Melee', 'Ranged'];
        return roles.map(role => {
            const counter = document.querySelector(`.role-${role.toLowerCase()} .counter-value`);
            return counter ? parseInt(counter.textContent) || 0 : 0;
        });
    }

    getClassData() {
        // Extract class data from table
        const classCount = {};
        const classColors = {
            'Death Knight': '#C41E3A',
            'Demon Hunter': '#A330C9',
            'Druid': '#FF7C0A',
            'Evoker': '#33937F',
            'Hunter': '#AAD372',
            'Mage': '#3FC7EB',
            'Monk': '#00FF98',
            'Paladin': '#F48CBA',
            'Priest': '#FFFFFF',
            'Rogue': '#FFF468',
            'Shaman': '#0070DD',
            'Warlock': '#8788EE',
            'Warrior': '#C69B6D'
        };

        // Count classes from table
        document.querySelectorAll('.table-modern tbody tr').forEach(row => {
            const classCell = row.cells[4]; // Assuming class is in 5th column
            if (classCell) {
                const className = classCell.textContent.trim();
                classCount[className] = (classCount[className] || 0) + 1;
            }
        });

        return {
            labels: Object.keys(classCount),
            values: Object.values(classCount),
            colors: Object.keys(classCount).map(cls => classColors[cls] || '#ffffff')
        };
    }

    setupRefreshButton() {
        const refreshBtn = document.getElementById('refreshData');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.refreshData();
            });
        }
    }

    async refreshData() {
        const refreshBtn = document.getElementById('refreshData');
        if (refreshBtn) {
            refreshBtn.classList.add('loading');
            refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Updating...';
        }

        try {
            const response = await fetch('/update_data', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            const result = await response.json();
            
            if (response.ok) {
                // Show success message
                this.showNotification('Data updated successfully!', 'success');
                // Reload page after short delay
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                this.showNotification(result.error || 'Failed to update data', 'error');
            }
        } catch (error) {
            this.showNotification('Network error occurred', 'error');
        } finally {
            if (refreshBtn) {
                refreshBtn.classList.remove('loading');
                refreshBtn.innerHTML = '<i class="fas fa-sync-alt me-2"></i>Refresh Data';
            }
        }
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 100px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new UproarApp();
});

// Export for global access
window.UproarApp = UproarApp;
