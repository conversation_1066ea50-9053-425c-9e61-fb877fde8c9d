<?xml version="1.0"?>
<doc>
    <assembly>
        <name>MySql.EntityFrameworkCore</name>
    </assembly>
    <members>
        <member name="T:MySql.EntityFrameworkCore.DataAnnotations.MySQLCharsetAttribute">
            <summary>
              Establishes the character set of an entity property.
            </summary>
        </member>
        <member name="P:MySql.EntityFrameworkCore.DataAnnotations.MySQLCharsetAttribute.Charset">
            <summary>
              Character set to use for the attribute.
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.DataAnnotations.MySQLCharsetAttribute.#ctor(System.String)">
            <summary>
              Initializes a new instance of the <see cref="T:MySql.EntityFrameworkCore.DataAnnotations.MySQLCharsetAttribute"/> class.
            </summary>
            <param name="charset">Character set to use.</param>
        </member>
        <member name="T:MySql.EntityFrameworkCore.DataAnnotations.MySQLCollationAttribute">
            <summary>
              Sets the collation in an entity property.
            </summary>
        </member>
        <member name="P:MySql.EntityFrameworkCore.DataAnnotations.MySQLCollationAttribute.Collation">
            <summary>
              Collation set in the attribute.
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.DataAnnotations.MySQLCollationAttribute.#ctor(System.String)">
            <summary>
              Initializes a new instance of the <see cref="T:MySql.EntityFrameworkCore.DataAnnotations.MySQLCollationAttribute"/> class.
            </summary>
            <param name="collation">Collation to use.</param>
        </member>
        <member name="T:MySql.EntityFrameworkCore.Diagnostics.MySQLEventId">
            <summary>
            <para>
              Event IDs for MySQL events that correspond to messages logged to an <see cref="T:Microsoft.Extensions.Logging.ILogger" />
              and events sent to a <see cref="T:System.Diagnostics.DiagnosticSource" />.
            </para>
            <para>
              These IDs are also used with <see cref="T:Microsoft.EntityFrameworkCore.Diagnostics.WarningsConfigurationBuilder" /> to configure the
              behavior of warnings.
            </para>
            </summary>
        </member>
        <member name="F:MySql.EntityFrameworkCore.Diagnostics.MySQLEventId.DecimalTypeDefaultWarning">
            <summary>
            <para>
              No explicit type for a decimal column.
            </para>
            <para>
              This event is in the <see cref="T:Microsoft.EntityFrameworkCore.DbLoggerCategory.Model.Validation" /> category.
            </para>
            <para>
              This event uses the <see cref="T:Microsoft.EntityFrameworkCore.Diagnostics.PropertyEventData" /> payload when used with a <see cref="T:System.Diagnostics.DiagnosticSource" />.
            </para>
            </summary>
        </member>
        <member name="F:MySql.EntityFrameworkCore.Diagnostics.MySQLEventId.ByteIdentityColumnWarning">
            <summary>
            <para>
              A byte property is set up to use a MySQL identity column.
            </para>
            <para>
              This event is in the <see cref="T:Microsoft.EntityFrameworkCore.DbLoggerCategory.Model.Validation" /> category.
            </para>
            <para>
              This event uses the <see cref="T:Microsoft.EntityFrameworkCore.Diagnostics.PropertyEventData" /> payload when used with a <see cref="T:System.Diagnostics.DiagnosticSource" />.
            </para>
            </summary>
        </member>
        <member name="F:MySql.EntityFrameworkCore.Diagnostics.MySQLEventId.ColumnFound">
            <summary>
              A column was found.
              This event is in the <see cref="T:Microsoft.EntityFrameworkCore.DbLoggerCategory.Scaffolding" /> category.
            </summary>
        </member>
        <member name="F:MySql.EntityFrameworkCore.Diagnostics.MySQLEventId.DefaultSchemaFound">
            <summary>
              A default schema was found.
              This event is in the <see cref="T:Microsoft.EntityFrameworkCore.DbLoggerCategory.Scaffolding" /> category.
            </summary>
        </member>
        <member name="F:MySql.EntityFrameworkCore.Diagnostics.MySQLEventId.TypeAliasFound">
            <summary>
              A type alias was found.
              This event is in the <see cref="T:Microsoft.EntityFrameworkCore.DbLoggerCategory.Scaffolding" /> category.
            </summary>
        </member>
        <member name="F:MySql.EntityFrameworkCore.Diagnostics.MySQLEventId.MissingSchemaWarning">
            <summary>
              The database is missing a schema.
              This event is in the <see cref="T:Microsoft.EntityFrameworkCore.DbLoggerCategory.Scaffolding" /> category.
            </summary>
        </member>
        <member name="F:MySql.EntityFrameworkCore.Diagnostics.MySQLEventId.MissingTableWarning">
            <summary>
              The database is missing a table.
              This event is in the <see cref="T:Microsoft.EntityFrameworkCore.DbLoggerCategory.Scaffolding" /> category.
            </summary>
        </member>
        <member name="F:MySql.EntityFrameworkCore.Diagnostics.MySQLEventId.ForeignKeyReferencesMissingPrincipalTableWarning">
            <summary>
              A foreign key references a missing table at the principal end.
              This event is in the <see cref="T:Microsoft.EntityFrameworkCore.DbLoggerCategory.Scaffolding" /> category.
            </summary>
        </member>
        <member name="F:MySql.EntityFrameworkCore.Diagnostics.MySQLEventId.TableFound">
            <summary>
              A table was found.
              This event is in the <see cref="T:Microsoft.EntityFrameworkCore.DbLoggerCategory.Scaffolding" /> category.
            </summary>
        </member>
        <member name="F:MySql.EntityFrameworkCore.Diagnostics.MySQLEventId.SequenceFound">
            <summary>
              A sequence was found.
              This event is in the <see cref="T:Microsoft.EntityFrameworkCore.DbLoggerCategory.Scaffolding" /> category.
            </summary>
        </member>
        <member name="F:MySql.EntityFrameworkCore.Diagnostics.MySQLEventId.PrimaryKeyFound">
            <summary>
              Primary key was found.
              This event is in the <see cref="T:Microsoft.EntityFrameworkCore.DbLoggerCategory.Scaffolding" /> category.
            </summary>
        </member>
        <member name="F:MySql.EntityFrameworkCore.Diagnostics.MySQLEventId.UniqueConstraintFound">
            <summary>
              An unique constraint was found.
              This event is in the <see cref="T:Microsoft.EntityFrameworkCore.DbLoggerCategory.Scaffolding" /> category.
            </summary>
        </member>
        <member name="F:MySql.EntityFrameworkCore.Diagnostics.MySQLEventId.IndexFound">
            <summary>
              An index was found.
              This event is in the <see cref="T:Microsoft.EntityFrameworkCore.DbLoggerCategory.Scaffolding" /> category.
            </summary>
        </member>
        <member name="F:MySql.EntityFrameworkCore.Diagnostics.MySQLEventId.ForeignKeyFound">
            <summary>
              A foreign key was found.
              This event is in the <see cref="T:Microsoft.EntityFrameworkCore.DbLoggerCategory.Scaffolding" /> category.
            </summary>
        </member>
        <member name="F:MySql.EntityFrameworkCore.Diagnostics.MySQLEventId.ForeignKeyPrincipalColumnMissingWarning">
            <summary>
              A principal column referenced by a foreign key was not found.
              This event is in the <see cref="T:Microsoft.EntityFrameworkCore.DbLoggerCategory.Scaffolding" /> category.
            </summary>
        </member>
        <member name="F:MySql.EntityFrameworkCore.Diagnostics.MySQLEventId.ReflexiveConstraintIgnored">
            <summary>
              A reflexive foreign key constraint was skipped.
              This event is in the <see cref="T:Microsoft.EntityFrameworkCore.DbLoggerCategory.Scaffolding" /> category.
            </summary>
        </member>
        <member name="T:MySql.EntityFrameworkCore.Extensions.MySQLDatabaseFacadeExtensions">
            <summary>
              MySQL specific extension methods for <see cref="P:Microsoft.EntityFrameworkCore.DbContext.Database" />.
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLDatabaseFacadeExtensions.IsMySql(Microsoft.EntityFrameworkCore.Infrastructure.DatabaseFacade)">
            <summary>
              <para>
              Indicates whether the database provider currently in use is the MySQL provider.
              </para>
              <para>
              This method can only be used after the <see cref="T:Microsoft.EntityFrameworkCore.DbContext" /> has been configured because
              it is only then that the provider is known. This method cannot be used
              in <see cref="M:Microsoft.EntityFrameworkCore.DbContext.OnConfiguring(Microsoft.EntityFrameworkCore.DbContextOptionsBuilder)" /> because this is where application code sets the
              provider to use as part of configuring the context.
              </para>
            </summary>
            <param name="database"> The facade from <see cref="P:Microsoft.EntityFrameworkCore.DbContext.Database" />. </param>
            <returns><see langword="true"/> if MySQL is being used; otherwise, <see langword="false"/>. </returns>
        </member>
        <member name="T:MySql.EntityFrameworkCore.Extensions.MySQLMatchSearchMode">
            <summary>
            Types of search to perform.
            </summary>
        </member>
        <member name="F:MySql.EntityFrameworkCore.Extensions.MySQLMatchSearchMode.NaturalLanguage">
            <summary>
              Performs a natural language search for a string against a text collection.
            </summary>
        </member>
        <member name="F:MySql.EntityFrameworkCore.Extensions.MySQLMatchSearchMode.NaturalLanguageWithQueryExpansion">
            <summary>
              Performs a natural language search with query expansion for a string against a text collection.
            </summary>
        </member>
        <member name="F:MySql.EntityFrameworkCore.Extensions.MySQLMatchSearchMode.Boolean">
            <summary>
              Performs a boolean search for a string against a text collection.
            </summary>
        </member>
        <member name="T:MySql.EntityFrameworkCore.Extensions.MySQLDbFunctionsExtensions">
            <summary>
              Provides CLR methods that are translated to database functions when used in a LINQ to Entities queries.
              The methods in this class are accessed with <see cref="P:Microsoft.EntityFrameworkCore.EF.Functions" />.
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLDbFunctionsExtensions.DateDiffYear(Microsoft.EntityFrameworkCore.DbFunctions,System.DateTime,System.DateTime)">
            <summary>
              Counts the number of year boundaries crossed between the startDate and endDate.
              Corresponds to TIMESTAMPDIFF(YEAR,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of year boundaries crossed between the dates.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLDbFunctionsExtensions.DateDiffYear(Microsoft.EntityFrameworkCore.DbFunctions,System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
              Counts the number of year boundaries crossed between the startDate and endDate.
              Corresponds to TIMESTAMPDIFF(YEAR,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of year boundaries crossed between the dates.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLDbFunctionsExtensions.DateDiffYear(Microsoft.EntityFrameworkCore.DbFunctions,System.DateTimeOffset,System.DateTimeOffset)">
            <summary>
              Counts the number of year boundaries crossed between the startDate and endDate.
              Corresponds to TIMESTAMPDIFF(YEAR,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of year boundaries crossed between the dates.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLDbFunctionsExtensions.DateDiffYear(Microsoft.EntityFrameworkCore.DbFunctions,System.Nullable{System.DateTimeOffset},System.Nullable{System.DateTimeOffset})">
            <summary>
              Counts the number of year boundaries crossed between the startDate and endDate.
              Corresponds to TIMESTAMPDIFF(YEAR,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of year boundaries crossed between the dates.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLDbFunctionsExtensions.DateDiffYear(Microsoft.EntityFrameworkCore.DbFunctions,System.DateOnly,System.DateOnly)">
            <summary>
                Counts the number of year boundaries crossed between the startDate and endDate.
                Corresponds to TIMESTAMPDIFF(YEAR,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of year boundaries crossed between the dates.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLDbFunctionsExtensions.DateDiffYear(Microsoft.EntityFrameworkCore.DbFunctions,System.Nullable{System.DateOnly},System.Nullable{System.DateOnly})">
            <summary>
                Counts the number of year boundaries crossed between the startDate and endDate.
                Corresponds to TIMESTAMPDIFF(YEAR,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of year boundaries crossed between the dates.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLDbFunctionsExtensions.DateDiffMonth(Microsoft.EntityFrameworkCore.DbFunctions,System.DateTime,System.DateTime)">
            <summary>
              Counts the number of month boundaries crossed between the startDate and endDate.
              Corresponds to TIMESTAMPDIFF(MONTH,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of month boundaries crossed between the dates.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLDbFunctionsExtensions.DateDiffMonth(Microsoft.EntityFrameworkCore.DbFunctions,System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
              Counts the number of month boundaries crossed between the startDate and endDate.
              Corresponds to TIMESTAMPDIFF(MONTH,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of month boundaries crossed between the dates.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLDbFunctionsExtensions.DateDiffMonth(Microsoft.EntityFrameworkCore.DbFunctions,System.DateTimeOffset,System.DateTimeOffset)">
            <summary>
              Counts the number of month boundaries crossed between the startDate and endDate.
              Corresponds to TIMESTAMPDIFF(MONTH,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of month boundaries crossed between the dates.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLDbFunctionsExtensions.DateDiffMonth(Microsoft.EntityFrameworkCore.DbFunctions,System.Nullable{System.DateTimeOffset},System.Nullable{System.DateTimeOffset})">
            <summary>
              Counts the number of month boundaries crossed between the startDate and endDate.
              Corresponds to TIMESTAMPDIFF(MONTH,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of month boundaries crossed between the dates.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLDbFunctionsExtensions.DateDiffMonth(Microsoft.EntityFrameworkCore.DbFunctions,System.DateOnly,System.DateOnly)">
            <summary>
                Counts the number of month boundaries crossed between the startDate and endDate.
                Corresponds to TIMESTAMPDIFF(MONTH,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of month boundaries crossed between the dates.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLDbFunctionsExtensions.DateDiffMonth(Microsoft.EntityFrameworkCore.DbFunctions,System.Nullable{System.DateOnly},System.Nullable{System.DateOnly})">
            <summary>
                Counts the number of month boundaries crossed between the startDate and endDate.
                Corresponds to TIMESTAMPDIFF(MONTH,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of month boundaries crossed between the dates.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLDbFunctionsExtensions.DateDiffDay(Microsoft.EntityFrameworkCore.DbFunctions,System.DateTime,System.DateTime)">
            <summary>
              Counts the number of day boundaries crossed between the startDate and endDate.
              Corresponds to TIMESTAMPDIFF(DAY,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of day boundaries crossed between the dates.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLDbFunctionsExtensions.DateDiffDay(Microsoft.EntityFrameworkCore.DbFunctions,System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
              Counts the number of day boundaries crossed between the startDate and endDate.
              Corresponds to TIMESTAMPDIFF(DAY,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of day boundaries crossed between the dates.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLDbFunctionsExtensions.DateDiffDay(Microsoft.EntityFrameworkCore.DbFunctions,System.DateTimeOffset,System.DateTimeOffset)">
            <summary>
              Counts the number of day boundaries crossed between the startDate and endDate.
              Corresponds to TIMESTAMPDIFF(DAY,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of day boundaries crossed between the dates.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLDbFunctionsExtensions.DateDiffDay(Microsoft.EntityFrameworkCore.DbFunctions,System.Nullable{System.DateTimeOffset},System.Nullable{System.DateTimeOffset})">
            <summary>
              Counts the number of day boundaries crossed between the startDate and endDate.
              Corresponds to TIMESTAMPDIFF(DAY,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of day boundaries crossed between the dates.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLDbFunctionsExtensions.DateDiffDay(Microsoft.EntityFrameworkCore.DbFunctions,System.DateOnly,System.DateOnly)">
            <summary>
                Counts the number of day boundaries crossed between the startDate and endDate.
                Corresponds to TIMESTAMPDIFF(DAY,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of day boundaries crossed between the dates.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLDbFunctionsExtensions.DateDiffDay(Microsoft.EntityFrameworkCore.DbFunctions,System.Nullable{System.DateOnly},System.Nullable{System.DateOnly})">
            <summary>
                Counts the number of day boundaries crossed between the startDate and endDate.
                Corresponds to TIMESTAMPDIFF(DAY,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of day boundaries crossed between the dates.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLDbFunctionsExtensions.DateDiffHour(Microsoft.EntityFrameworkCore.DbFunctions,System.DateTime,System.DateTime)">
            <summary>
              Counts the number of hour boundaries crossed between the startDate and endDate.
              Corresponds to TIMESTAMPDIFF(HOUR,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of hour boundaries crossed between the dates.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLDbFunctionsExtensions.DateDiffHour(Microsoft.EntityFrameworkCore.DbFunctions,System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
              Counts the number of hour boundaries crossed between the startDate and endDate.
              Corresponds to TIMESTAMPDIFF(HOUR,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of hour boundaries crossed between the dates.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLDbFunctionsExtensions.DateDiffHour(Microsoft.EntityFrameworkCore.DbFunctions,System.DateTimeOffset,System.DateTimeOffset)">
            <summary>
              Counts the number of hour boundaries crossed between the startDate and endDate.
              Corresponds to TIMESTAMPDIFF(HOUR,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of hour boundaries crossed between the dates.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLDbFunctionsExtensions.DateDiffHour(Microsoft.EntityFrameworkCore.DbFunctions,System.Nullable{System.DateTimeOffset},System.Nullable{System.DateTimeOffset})">
            <summary>
              Counts the number of hour boundaries crossed between the startDate and endDate.
              Corresponds to TIMESTAMPDIFF(HOUR,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of hour boundaries crossed between the dates.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLDbFunctionsExtensions.DateDiffMinute(Microsoft.EntityFrameworkCore.DbFunctions,System.DateTime,System.DateTime)">
            <summary>
              Counts the number of minute boundaries crossed between the startDate and endDate.
              Corresponds to TIMESTAMPDIFF(MINUTE,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of minute boundaries crossed between the dates.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLDbFunctionsExtensions.DateDiffMinute(Microsoft.EntityFrameworkCore.DbFunctions,System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
              Counts the number of minute boundaries crossed between the startDate and endDate.
              Corresponds to TIMESTAMPDIFF(MINUTE,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of minute boundaries crossed between the dates.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLDbFunctionsExtensions.DateDiffMinute(Microsoft.EntityFrameworkCore.DbFunctions,System.DateTimeOffset,System.DateTimeOffset)">
            <summary>
              Counts the number of minute boundaries crossed between the startDate and endDate.
              Corresponds to TIMESTAMPDIFF(MINUTE,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of minute boundaries crossed between the dates.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLDbFunctionsExtensions.DateDiffMinute(Microsoft.EntityFrameworkCore.DbFunctions,System.Nullable{System.DateTimeOffset},System.Nullable{System.DateTimeOffset})">
            <summary>
              Counts the number of minute boundaries crossed between the startDate and endDate.
              Corresponds to TIMESTAMPDIFF(MINUTE,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of minute boundaries crossed between the dates.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLDbFunctionsExtensions.DateDiffSecond(Microsoft.EntityFrameworkCore.DbFunctions,System.DateTime,System.DateTime)">
            <summary>
              Counts the number of boundaries (in seconds) crossed between the startDate and endDate.
              Corresponds to TIMESTAMPDIFF(SECOND,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of boundaries (in seconds) crossed between the dates.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLDbFunctionsExtensions.DateDiffSecond(Microsoft.EntityFrameworkCore.DbFunctions,System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
              Counts the number of boundaries (in seconds) crossed between the startDate and endDate.
              Corresponds to TIMESTAMPDIFF(SECOND,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of boundaries (in seconds) crossed between the dates.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLDbFunctionsExtensions.DateDiffSecond(Microsoft.EntityFrameworkCore.DbFunctions,System.DateTimeOffset,System.DateTimeOffset)">
            <summary>
              Counts the number of boundaries (in seconds) crossed between the startDate and endDate.
              Corresponds to TIMESTAMPDIFF(SECOND,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of boundaries (in seconds) crossed between the dates.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLDbFunctionsExtensions.DateDiffSecond(Microsoft.EntityFrameworkCore.DbFunctions,System.Nullable{System.DateTimeOffset},System.Nullable{System.DateTimeOffset})">
            <summary>
              Counts the number of boundaries (in seconds) crossed between the startDate and endDate.
              Corresponds to TIMESTAMPDIFF(SECOND,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of boundaries (in seconds) crossed between the dates.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLDbFunctionsExtensions.DateDiffMicrosecond(Microsoft.EntityFrameworkCore.DbFunctions,System.DateTime,System.DateTime)">
            <summary>
              Counts the number of microsecond boundaries crossed between the startDate and endDate.
              Corresponds to TIMESTAMPDIFF(MICROSECOND,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of microsecond boundaries crossed between the dates.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLDbFunctionsExtensions.DateDiffMicrosecond(Microsoft.EntityFrameworkCore.DbFunctions,System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
              Counts the number of microsecond boundaries crossed between the startDate and endDate.
              Corresponds to TIMESTAMPDIFF(MICROSECOND,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of microsecond boundaries crossed between the dates.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLDbFunctionsExtensions.DateDiffMicrosecond(Microsoft.EntityFrameworkCore.DbFunctions,System.DateTimeOffset,System.DateTimeOffset)">
            <summary>
              Counts the number of microsecond boundaries crossed between the startDate and endDate.
              Corresponds to TIMESTAMPDIFF(MICROSECOND,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of microsecond boundaries crossed between the dates.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLDbFunctionsExtensions.DateDiffMicrosecond(Microsoft.EntityFrameworkCore.DbFunctions,System.Nullable{System.DateTimeOffset},System.Nullable{System.DateTimeOffset})">
            <summary>
              Counts the number of microsecond boundaries crossed between the startDate and endDate.
              Corresponds to TIMESTAMPDIFF(MICROSECOND,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of microsecond boundaries crossed between the dates.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLDbFunctionsExtensions.Like``1(Microsoft.EntityFrameworkCore.DbFunctions,``0,System.String)">
            <summary>
              <para>
              An implementation of the SQL LIKE operation. In relational databases this is usually directly
              translated to SQL.
              </para>
              <para>
              Note that if this function is translated into SQL, then the semantics of the comparison 
              depends on the database configuration. In particular, it may be either case-sensitive or
              case-insensitive. If this function is evaluated on the client, then it always uses
              a case-insensitive comparison.
              </para>
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="matchExpression">The property of entity that is to be matched.</param>
            <param name="pattern">A pattern that may involve wildcards %,_,[,],^.</param>
            <returns><see langword="true"/> if there is a match; otherwise, <see langword="false"/>.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLDbFunctionsExtensions.Like``1(Microsoft.EntityFrameworkCore.DbFunctions,``0,System.String,System.String)">
            <summary>
              <para>
              An implementation of the SQL LIKE operation. In relational databases, this is usually directly
              translated to SQL.
              </para>
              <para>
              Note that if this function is translated into SQL, then the semantics of the comparison
              depends on the database configuration. In particular, it may be either case-sensitive or
              case-insensitive. If this function is evaluated on the client, then it always uses
              a case-insensitive comparison.
              </para>
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="matchExpression">The property of entity that is to be matched.</param>
            <param name="pattern">A pattern that may involve wildcards %,_,[,],^.</param>
            <param name="escapeCharacter">
              The escape character (as a single character string) to use in front of %,_,[,],^
              if they are not used as wildcards.
            </param>
            <returns><see langword="true"/> if there is a match; otherwise, <see langword="false"/>.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLDbFunctionsExtensions.Match(Microsoft.EntityFrameworkCore.DbFunctions,System.String[],System.String,MySql.EntityFrameworkCore.Extensions.MySQLMatchSearchMode)">
            <summary>
              <para>
              An implementation of the SQL MATCH function used to perform a natural language search for a string against a text collection.
              A collection is a set of one or more columns included in a FULLTEXT index.
              </para>
              <para>
              MATCH (col1,col2,...) AGAINST (expr [search_modifier])
              </para>
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="properties">The columns of the entity that is to be matched.</param>
            <param name="pattern">A pattern that may involve wildcards %,_,[,],^.</param>
            <param name="searchMode">
              Indicates what type of search to perform
            </param>
            <returns><see langword="true"/> if there is a match; otherwise, <see langword="false"/>.</returns>
        </member>
        <member name="T:MySql.EntityFrameworkCore.Extensions.MySQLEntityTypeBuilderExtensions">
            <summary>
              MySQL specific extension methods for <see cref="T:Microsoft.EntityFrameworkCore.Metadata.Builders.EntityTypeBuilder" />.
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLEntityTypeBuilderExtensions.HasCharSet(Microsoft.EntityFrameworkCore.Metadata.Builders.EntityTypeBuilder,System.String)">
            <summary>
              Sets the MySQL character set on the table associated with this entity. When you only specify the character set, MySQL implicitly
              sets the proper collation as well.
            </summary>
            <param name="entityTypeBuilder"> The builder for the entity type being configured. </param>
            <param name="charSet"> The name of the character set. </param>
            <returns> The same builder instance so that multiple calls can be chained. </returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLEntityTypeBuilderExtensions.HasCharSet``1(Microsoft.EntityFrameworkCore.Metadata.Builders.EntityTypeBuilder{``0},System.String)">
            <summary>
              Sets the MySQL character set on the table associated with this entity. When you only specify the character set, MySQL implicitly
              sets the proper collation as well.
            </summary>
            <param name="entityTypeBuilder"> The builder for the entity type being configured. </param>
            <param name="charSet"> The name of the character set. </param>
            <returns> The same builder instance so that multiple calls can be chained. </returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLEntityTypeBuilderExtensions.HasCharSet(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionEntityTypeBuilder,System.String,System.Boolean)">
            <summary>
              Sets the MySQL character set on the table associated with this entity. When you only specify the character set, MySQL implicitly
              sets the proper collation as well.
            </summary>
            <param name="entityTypeBuilder"> The builder for the entity type being configured. </param>
            <param name="charSet"> The name of the character set. </param>
            <param name="fromDataAnnotation">Indicates whether the configuration was specified using a data annotation.</param>
            <returns> The same builder instance so that multiple calls can be chained. </returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLEntityTypeBuilderExtensions.CanSetCharSet(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionEntityTypeBuilder,System.String,System.Boolean)">
            <summary>
              Returns a value indicating whether the MySQL character set can be set on the table associated with this entity.
            </summary>
            <param name="entityTypeBuilder"> The builder for the entity type being configured. </param>
            <param name="charSet"> The name of the character set. </param>
            <param name="fromDataAnnotation">Indicates whether the configuration was specified using a data annotation.</param>
            <returns><see langword="true"/> if the mapped table can be configured with the collation.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLEntityTypeBuilderExtensions.UseCollation(Microsoft.EntityFrameworkCore.Metadata.Builders.EntityTypeBuilder,System.String)">
            <summary>
              Sets the MySQL collation on the table associated with this entity. When you only specify the collation, MySQL implicitly sets
              the proper character set as well.
            </summary>
            <param name="entityTypeBuilder"> The builder for the entity type being configured. </param>
            <param name="collation"> The name of the collation. </param>
            <returns> The same builder instance so that multiple calls can be chained. </returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLEntityTypeBuilderExtensions.UseCollation``1(Microsoft.EntityFrameworkCore.Metadata.Builders.EntityTypeBuilder{``0},System.String)">
            <summary>
              Sets the MySQL collation on the table associated with this entity. When you only specify the collation, MySQL implicitly sets
              the proper character set as well.
            </summary>
            <param name="entityTypeBuilder"> The builder for the entity type being configured. </param>
            <param name="collation"> The name of the collation. </param>
            <returns> The same builder instance so that multiple calls can be chained. </returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLEntityTypeBuilderExtensions.UseCollation(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionEntityTypeBuilder,System.String,System.Boolean)">
            <summary>
              Sets the MySQL collation on the table associated with this entity. When you only specify the collation, MySQL implicitly sets
              the proper character set as well.
            </summary>
            <param name="entityTypeBuilder"> The builder for the entity type being configured. </param>
            <param name="collation"> The name of the collation. </param>
            <param name="fromDataAnnotation">Indicates whether the configuration was specified using a data annotation.</param>
            <returns> The same builder instance so that multiple calls can be chained. </returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLEntityTypeBuilderExtensions.CanSetCollation(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionEntityTypeBuilder,System.String,System.Boolean)">
            <summary>
              Returns a value indicating whether the MySQL collation can be set on the table associated with this entity.
            </summary>
            <param name="entityTypeBuilder"> The builder for the entity type being configured. </param>
            <param name="collation"> The name of the collation. </param>
            <param name="fromDataAnnotation">Indicates whether the configuration was specified using a data annotation.</param>
            <returns><see langword="true"/> if the mapped table can be configured with the collation.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLEntityTypeBuilderExtensions.CanSetCollationDelegation(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionEntityTypeBuilder,System.Boolean)">
            <summary>
              Returns a value indicating whether the given collation delegation modes can be set.
            </summary>
            <param name="entityTypeBuilder"> The builder for the entity type being configured. </param>
            <param name="fromDataAnnotation">Indicates whether the configuration was specified using a data annotation.</param>
            <returns> <see langword="true" /> if the given collation delegation modes can be set as default. </returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLEntityTypeBuilderExtensions.HasTableOption(Microsoft.EntityFrameworkCore.Metadata.Builders.EntityTypeBuilder,System.String,System.String)">
            <summary>
              Sets a table option for the table associated with this entity.
              Can be called more than once to set multiple table options.
            </summary>
            <param name="entityTypeBuilder"> The builder for the entity type being configured. </param>
            <param name="name"> The name of the table options. </param>
            <param name="value"> The value of the table options. </param>
            <returns> The same builder instance so that multiple calls can be chained. </returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLEntityTypeBuilderExtensions.HasTableOption``1(Microsoft.EntityFrameworkCore.Metadata.Builders.EntityTypeBuilder{``0},System.String,System.String)">
            <summary>
              Sets a table option for the table associated with this entity.
              Can be called more than once to set multiple table options.
            </summary>
            <param name="entityTypeBuilder"> The builder for the entity type being configured. </param>
            <param name="name"> The name of the table options. </param>
            <param name="value"> The value of the table options. </param>
            <returns> The same builder instance so that multiple calls can be chained. </returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLEntityTypeBuilderExtensions.HasTableOption(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionEntityTypeBuilder,System.String,System.String,System.Boolean)">
            <summary>
              Sets a table option for the table associated with this entity.
              Can be called more than once to set multiple table options.
            </summary>
            <param name="entityTypeBuilder"> The builder for the entity type being configured. </param>
            <param name="name"> The name of the table options. </param>
            <param name="value"> The value of the table options. </param>
            <param name="fromDataAnnotation">Indicates whether the configuration was specified using a data annotation.</param>
            <returns> The same builder instance so that multiple calls can be chained. </returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLEntityTypeBuilderExtensions.CanSetTableOption(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionEntityTypeBuilder,System.String,System.String,System.Boolean)">
            <summary>
              Returns a value indicating whether the table options for the table associated with this entity can be set.
            </summary>
            <param name="entityTypeBuilder"> The builder for the entity type being configured. </param>
            <param name="name"> The name of the table options. </param>
            <param name="value"> The value of the table options. </param>
            <param name="fromDataAnnotation">Indicates whether the configuration was specified using a data annotation.</param>
            <returns><see langword="true"/> if the value can be set.</returns>
        </member>
        <member name="T:MySql.EntityFrameworkCore.Extensions.MySQLEntityTypeExtensions">
            <summary>
              MySQL specific extension methods for entity types.
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLEntityTypeExtensions.GetCharSet(Microsoft.EntityFrameworkCore.Metadata.IReadOnlyEntityType)">
            <summary>
              Get the MySQL character set for the table associated with this entity.
            </summary>
            <param name="entityType"> The entity type. </param>
            <returns> The name of the character set. </returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLEntityTypeExtensions.SetCharSet(Microsoft.EntityFrameworkCore.Metadata.IMutableEntityType,System.String)">
            <summary>
              Sets the MySQL character set on the table associated with this entity. When you only specify the character set, MySQL implicitly
              sets the proper collation as well.
            </summary>
            <param name="entityType"> The entity type. </param>
            <param name="charSet"> The name of the character set. </param>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLEntityTypeExtensions.SetCharSet(Microsoft.EntityFrameworkCore.Metadata.IConventionEntityType,System.String,System.Boolean)">
            <summary>
              Sets the MySQL character set on the table associated with this entity. When you only specify the character set, MySQL implicitly
              sets the proper collation as well.
            </summary>
            <param name="entityType"> The entity type. </param>
            <param name="charSet"> The name of the character set. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
            <returns> The configured value. </returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLEntityTypeExtensions.GetCharSetConfigurationSource(Microsoft.EntityFrameworkCore.Metadata.IConventionEntityType)">
            <summary>
              Gets the configuration source for the character-set mode.
            </summary>
            <param name="entityType"> The entity type. </param>
            <returns> The configuration source. </returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLEntityTypeExtensions.GetCollation(Microsoft.EntityFrameworkCore.Metadata.IReadOnlyEntityType)">
            <summary>
              Get the MySQL collation for the table associated with this entity.
            </summary>
            <param name="entityType"> The entity type. </param>
            <returns> The name of the collation. </returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLEntityTypeExtensions.SetCollation(Microsoft.EntityFrameworkCore.Metadata.IMutableEntityType,System.String)">
            <summary>
              Sets the MySQL collation on the table associated with this entity. When you specify the collation, MySQL implicitly sets the
              proper character set as well.
            </summary>
            <param name="entityType"> The entity type. </param>
            <param name="collation"> The name of the collation. </param>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLEntityTypeExtensions.SetCollation(Microsoft.EntityFrameworkCore.Metadata.IConventionEntityType,System.String,System.Boolean)">
            <summary>
              Sets the MySQL collation on the table associated with this entity. When you specify the collation, MySQL implicitly sets the
              proper character set as well.
            </summary>
            <param name="entityType"> The entity type. </param>
            <param name="collation"> The name of the collation. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
            <returns> The configured value. </returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLEntityTypeExtensions.GetCollationConfigurationSource(Microsoft.EntityFrameworkCore.Metadata.IConventionEntityType)">
            <summary>
              Gets the configuration source for the collation mode.
            </summary>
            <param name="entityType"> The entity type. </param>
            <returns> The configuration source. </returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLEntityTypeExtensions.GetTableOptions(Microsoft.EntityFrameworkCore.Metadata.IReadOnlyEntityType)">
            <summary>
              Gets the MySQL table options for the table associated with this entity.
            </summary>
            <param name="entityType"> The entity type. </param>
            <returns> A dictionary of table options. </returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLEntityTypeExtensions.SetTableOptions(Microsoft.EntityFrameworkCore.Metadata.IMutableEntityType,System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
              Sets the MySQL table options for the table associated with this entity.
            </summary>
            <param name="entityType"> The entity type. </param>
            <param name="options"> A dictionary of table options. </param>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLEntityTypeExtensions.SetTableOptions(Microsoft.EntityFrameworkCore.Metadata.IConventionEntityType,System.Collections.Generic.Dictionary{System.String,System.String},System.Boolean)">
            <summary>
              Sets the MySQL table options for the table associated with this entity.
            </summary>
            <param name="entityType"> The entity type. </param>
            <param name="options"> A dictionary of table options. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
            <returns> The configured value. </returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLEntityTypeExtensions.GetTableOptionsConfigurationSource(Microsoft.EntityFrameworkCore.Metadata.IConventionEntityType)">
            <summary>
              Gets the configuration source for the table options.
            </summary>
            <param name="entityType"> The entity type. </param>
            <returns> The configuration source. </returns>
        </member>
        <member name="T:MySql.EntityFrameworkCore.Extensions.MySqlIndexBuilderExtensions">
            <summary>
            MySQL-specific extension methods for <see cref="T:Microsoft.EntityFrameworkCore.Metadata.Builders.IndexBuilder"/>.
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySqlIndexBuilderExtensions.IsFullText(Microsoft.EntityFrameworkCore.Metadata.Builders.IndexBuilder,System.Boolean,System.String)">
            <summary>
            Sets a value indicating whether the index is full text.
            </summary>
            <param name="indexBuilder"> The index builder. </param>
            <param name="fullText"> The value to set. </param>
            <param name="parser"> An optional argument (for example, "ngram"), that will be used in an `WITH PARSER` clause. </param>
            <returns> The index builder. </returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySqlIndexBuilderExtensions.IsFullText``1(Microsoft.EntityFrameworkCore.Metadata.Builders.IndexBuilder{``0},System.Boolean,System.String)">
            <summary>
            Sets a value indicating whether the index is full text.
            </summary>
            <param name="indexBuilder"> The index builder. </param>
            <param name="fullText"> The value to set. </param>
            <param name="parser"> An optional argument (for example, "ngram") that will be used in an `WITH PARSER` clause. </param>
            <returns> The index builder. </returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySqlIndexBuilderExtensions.IsSpatial(Microsoft.EntityFrameworkCore.Metadata.Builders.IndexBuilder,System.Boolean)">
            <summary>
            Sets a value indicating whether the index is spartial.
            </summary>
            <param name="indexBuilder"> The index builder. </param>
            <param name="spatial"> The value to set. </param>
            <returns> The index builder. </returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySqlIndexBuilderExtensions.IsSpatial``1(Microsoft.EntityFrameworkCore.Metadata.Builders.IndexBuilder{``0},System.Boolean)">
            <summary>
            Sets a value indicating whether the index is spartial.
            </summary>
            <param name="indexBuilder"> The index builder. </param>
            <param name="spatial"> The value to set. </param>
            <returns> The index builder. </returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySqlIndexBuilderExtensions.HasPrefixLength(Microsoft.EntityFrameworkCore.Metadata.Builders.IndexBuilder,System.Int32[])">
            <summary>
            Sets prefix lengths for the index.
            </summary>
            <param name="indexBuilder"> The index builder. </param>
            <param name="prefixLengths">The prefix lengths to set in the order of the index columns where specified.
            A value of `0` indicates, that the full length should be used for that column. </param>
            <returns> The index builder. </returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySqlIndexBuilderExtensions.HasPrefixLength``1(Microsoft.EntityFrameworkCore.Metadata.Builders.IndexBuilder{``0},System.Int32[])">
            <summary>
            Sets prefix lengths for the index.
            </summary>
            <param name="indexBuilder"> The index builder. </param>
            <param name="prefixLengths">The prefix lengths to set in the order of the index columns where specified.
            A value of `0` indicates, that the full length should be used for that column. </param>
            <returns> The index builder. </returns>
        </member>
        <member name="T:MySql.EntityFrameworkCore.Extensions.MySQLIndexExtensions">
            <summary>
              Extension methods for <see cref="T:Microsoft.EntityFrameworkCore.Metadata.IIndex" /> for MySQL specific metadata.
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLIndexExtensions.IsFullText(Microsoft.EntityFrameworkCore.Metadata.IIndex)">
            <summary>
              Returns a value indicating whether the index is full text.
            </summary>
            <param name="index">The index.</param>
            <returns><see langword="true"/> if the index is clustered; otherwise, <see langword="false"/>.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLIndexExtensions.SetIsFullText(Microsoft.EntityFrameworkCore.Metadata.IMutableIndex,System.Nullable{System.Boolean})">
            <summary>
              Sets a value indicating whether the index is full text.
            </summary>
            <param name="value">The value to set.</param>
            <param name="index">The index.</param>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLIndexExtensions.SetIsFullText(Microsoft.EntityFrameworkCore.Metadata.IConventionIndex,System.Nullable{System.Boolean},System.Boolean)">
            <summary>
              Sets a value indicating whether the index is full text.
            </summary>
            <param name="value">The value to set.</param>
            <param name="index">The index.</param>
            <param name="fromDataAnnotation">Indicates whether the configuration was specified using a data annotation.</param>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLIndexExtensions.GetIsFullTextConfigurationSource(Microsoft.EntityFrameworkCore.Metadata.IConventionIndex)">
            <summary>
              Returns the <see cref="T:Microsoft.EntityFrameworkCore.Metadata.ConfigurationSource" /> for whether the index is full text.
            </summary>
            <param name="property">The property.</param>
            <returns>The <see cref="T:Microsoft.EntityFrameworkCore.Metadata.ConfigurationSource" /> for whether the index is full text.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLIndexExtensions.FullTextParser(Microsoft.EntityFrameworkCore.Metadata.IIndex)">
            <summary>
              Returns a value indicating which full text parser to use.
            </summary>
            <param name="index"> The index. </param>
            <returns> The name of the full text parser. </returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLIndexExtensions.SetFullTextParser(Microsoft.EntityFrameworkCore.Metadata.IMutableIndex,System.String)">
            <summary>
              Sets a value indicating which full text parser to be used.
            </summary>
            <param name="value"> The value to set. </param>
            <param name="index"> The index. </param>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLIndexExtensions.SetFullTextParser(Microsoft.EntityFrameworkCore.Metadata.IConventionIndex,System.String,System.Boolean)">
            <summary>
              Sets a value indicating which full text parser to be used.
            </summary>
            <param name="value"> The value to set. </param>
            <param name="index"> The index. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLIndexExtensions.GetFullTextParserConfigurationSource(Microsoft.EntityFrameworkCore.Metadata.IConventionIndex)">
            <summary>
              Returns the <see cref="T:Microsoft.EntityFrameworkCore.Metadata.ConfigurationSource" /> for whether the index is full text.
            </summary>
            <param name="property"> The property. </param>
            <returns> The <see cref="T:Microsoft.EntityFrameworkCore.Metadata.ConfigurationSource" /> for whether the index is full text. </returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLIndexExtensions.IsSpatial(Microsoft.EntityFrameworkCore.Metadata.IIndex)">
            <summary>
              Returns a value indicating whether the index is spartial.
            </summary>
            <param name="index">The index.</param>
            <returns><see langword="true"/> if the index is clustered; otherwise, <see langword="false"/></returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLIndexExtensions.SetIsSpatial(Microsoft.EntityFrameworkCore.Metadata.IMutableIndex,System.Nullable{System.Boolean})">
            <summary>
              Sets a value indicating whether the index is spartial.
            </summary>
            <param name="value">The value to set.</param>
            <param name="index">The index.</param>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLIndexExtensions.SetIsSpatial(Microsoft.EntityFrameworkCore.Metadata.IConventionIndex,System.Nullable{System.Boolean},System.Boolean)">
            <summary>
              Sets a value indicating whether the index is spartial.
            </summary>
            <param name="value">The value to set.</param>
            <param name="index">The index.</param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation.</param>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLIndexExtensions.GetIsSpatialConfigurationSource(Microsoft.EntityFrameworkCore.Metadata.IConventionIndex)">
            <summary>
              Indicates whether the index is spatial.
            </summary>
            <param name="property">The property.</param>
            <returns>The <see cref="T:Microsoft.EntityFrameworkCore.Metadata.ConfigurationSource" /> to show if the index is spartial.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLIndexExtensions.PrefixLength(Microsoft.EntityFrameworkCore.Metadata.IIndex)">
            <summary>
              Returns prefix lengths for the index.
            </summary>
            <param name="index"> The index. </param>
            <returns> The prefix lengths.
            A value of `0` indicates, that the full length should be used for that column. </returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLIndexExtensions.SetPrefixLength(Microsoft.EntityFrameworkCore.Metadata.IMutableIndex,System.Int32[])">
            <summary>
              Sets prefix lengths for the index.
            </summary>
            <param name="values"> The prefix lengths to set.
            A value of `0` indicates, that the full length should be used for that column. </param>
            <param name="index"> The index. </param>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLIndexExtensions.SetPrefixLength(Microsoft.EntityFrameworkCore.Metadata.IConventionIndex,System.Int32[],System.Boolean)">
            <summary>
              Sets prefix lengths for the index.
            </summary>
            <param name="values"> The prefix lengths to set.
            A value of `0` indicates, that the full length should be used for that column. </param>
            <param name="index"> The index. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLIndexExtensions.GetPrefixLengthConfigurationSource(Microsoft.EntityFrameworkCore.Metadata.IConventionIndex)">
            <summary>
              Returns the <see cref="T:Microsoft.EntityFrameworkCore.Metadata.ConfigurationSource" /> for prefix lengths of the index.
            </summary>
            <param name="property"> The property. </param>
            <returns> The <see cref="T:Microsoft.EntityFrameworkCore.Metadata.ConfigurationSource" /> for prefix lengths of the index. </returns>
        </member>
        <member name="T:MySql.EntityFrameworkCore.Extensions.MySQLKeyBuilderExtensions">
            <summary>
            MySQL specific extension methods for <see cref="T:Microsoft.EntityFrameworkCore.Metadata.Builders.KeyBuilder"/>.
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLKeyBuilderExtensions.HasPrefixLength(Microsoft.EntityFrameworkCore.Metadata.Builders.KeyBuilder,System.Int32[])">
            <summary>
            Sets prefix lengths for the key.
            </summary>
            <param name="keyBuilder"> The key builder. </param>
            <param name="prefixLengths">The prefix lengths to set in the order of the key columns where specified.
            A value of `0` indicates, that the full length should be used for that column. </param>
            <returns> The key builder. </returns>
        </member>
        <member name="T:MySql.EntityFrameworkCore.Extensions.MySQLKeyExtensions">
            <summary>
              Extension methods for <see cref="T:Microsoft.EntityFrameworkCore.Metadata.IKey" /> for MySQL-specific metadata.
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLKeyExtensions.PrefixLength(Microsoft.EntityFrameworkCore.Metadata.IKey)">
            <summary>
              Returns prefix lengths for the key.
            </summary>
            <param name="key"> The key. </param>
            <returns> The prefix lengths.
            A value of `0` indicates, that the full length should be used for that column. </returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLKeyExtensions.SetPrefixLength(Microsoft.EntityFrameworkCore.Metadata.IMutableKey,System.Int32[])">
            <summary>
              Sets prefix lengths for the key.
            </summary>
            <param name="values"> The prefix lengths to set.
            A value of `0` indicates, that the full length should be used for that column. </param>
            <param name="key"> The key. </param>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLKeyExtensions.SetPrefixLength(Microsoft.EntityFrameworkCore.Metadata.IConventionKey,System.Int32[],System.Boolean)">
            <summary>
              Sets prefix lengths for the key.
            </summary>
            <param name="values"> The prefix lengths to set.
            A value of `0` indicates, that the full length should be used for that column. </param>
            <param name="key"> The key. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLKeyExtensions.GetPrefixLengthConfigurationSource(Microsoft.EntityFrameworkCore.Metadata.IConventionKey)">
            <summary>
              Returns the <see cref="T:Microsoft.EntityFrameworkCore.Metadata.ConfigurationSource" /> for prefix lengths of the key.
            </summary>
            <param name="property"> The property. </param>
            <returns> The <see cref="T:Microsoft.EntityFrameworkCore.Metadata.ConfigurationSource" /> for prefix lengths of the key. </returns>
        </member>
        <member name="T:MySql.EntityFrameworkCore.Extensions.MySQLMigrationBuilderExtensions">
            <summary>
              MySQL extension methods for <see cref="T:Microsoft.EntityFrameworkCore.Migrations.MigrationBuilder" />.
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLMigrationBuilderExtensions.IsMySql(Microsoft.EntityFrameworkCore.Migrations.MigrationBuilder)">
            <summary>
              Indicates whether the database provider currently in use is the MySQL provider.
            </summary>
            <param name="migrationBuilder">The migrationBuilder from the parameters on <see cref="M:Microsoft.EntityFrameworkCore.Migrations.Migration.Up(Microsoft.EntityFrameworkCore.Migrations.MigrationBuilder)" /> 
              or <see cref="M:Microsoft.EntityFrameworkCore.Migrations.Migration.Down(Microsoft.EntityFrameworkCore.Migrations.MigrationBuilder)" />.</param>
            <returns><see langword="true"/> if MySQL is being used; otherwise, <see langword="false"/>.</returns>
        </member>
        <member name="T:MySql.EntityFrameworkCore.Extensions.MySQLModelBuilderExtensions">
            <summary>
            MySQL-specific extension methods for <see cref="T:Microsoft.EntityFrameworkCore.ModelBuilder"/>.
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLModelBuilderExtensions.HasCharSet(Microsoft.EntityFrameworkCore.ModelBuilder,System.String)">
            <summary>
              Sets the default character set to use for the model or database.
            </summary>
            <param name="modelBuilder">The model builder.</param>
            <param name="charSet">The name of the character set to use.</param>
            <returns>The same builder instance so that multiple calls can be chained.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLModelBuilderExtensions.HasCharSet(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionModelBuilder,System.String,System.Boolean)">
            <summary>
              Sets the default character set to use for the model or database.
            </summary>
            <param name="modelBuilder">The model builder.</param>
            <param name="charSet">The name of the character set to use.</param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
            <returns>
              The same builder instance if the configuration was applied,
              <see langword="null" /> otherwise.
            </returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLModelBuilderExtensions.CanSetCharSet(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionModelBuilder,System.String,System.Boolean)">
            <summary>
              Returns a value indicating whether the given character set can be set as default.
            </summary>
            <param name="modelBuilder"> The model builder. </param>
            <param name="charSet"> The character set. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
            <returns> <see langword="true" /> if the given character set can be set as default. </returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLModelBuilderExtensions.UseCollation(Microsoft.EntityFrameworkCore.ModelBuilder,System.String)">
            <summary>
              Configures the database collation, which will be used by all columns without an explicit collation. Also finely controls
              where to apply the collation recursively (including this model or database).
            </summary>
            <param name="modelBuilder"> The model builder. </param>
            <param name="collation"> The collation. </param>
            <returns> The same builder instance so that multiple calls can be chained. </returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLModelBuilderExtensions.UseCollation(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionModelBuilder,System.String,System.Boolean)">
            <summary>
              Configures the database collation, which will be used by all columns without an explicit collation. Also finely controls
              where to apply the collation recursively (including this model or database).
            </summary>
            <param name="modelBuilder"> The model builder. </param>
            <param name="collation"> The collation. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
            <returns>
              The same builder instance if the configuration was applied,
              <see langword="null" /> otherwise.
            </returns>
        </member>
        <member name="T:MySql.EntityFrameworkCore.Extensions.MySQLModelExtensions">
            <summary>
              Extension methods for <see cref="T:Microsoft.EntityFrameworkCore.Metadata.IModel" /> for MySQL-specific metadata.
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLModelExtensions.GetValueGenerationStrategy(Microsoft.EntityFrameworkCore.Metadata.IModel)">
            <summary>
              Returns the <see cref="T:MySql.EntityFrameworkCore.Metadata.MySQLValueGenerationStrategy" /> to use for properties
              of keys in the model, unless the property has a strategy explicitly set.
            </summary>
            <param name="model"> The model. </param>
            <returns> The default <see cref="T:MySql.EntityFrameworkCore.Metadata.MySQLValueGenerationStrategy" />. </returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLModelExtensions.SetValueGenerationStrategy(Microsoft.EntityFrameworkCore.Metadata.IMutableModel,System.Nullable{MySql.EntityFrameworkCore.Metadata.MySQLValueGenerationStrategy})">
            <summary>
              Attempts to set the <see cref="T:MySql.EntityFrameworkCore.Metadata.MySQLValueGenerationStrategy" /> to use for properties
              of keys in the model that do not have a strategy explicitly set.
            </summary>
            <param name="model"> The model. </param>
            <param name="value"> The value to set. </param>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLModelExtensions.SetValueGenerationStrategy(Microsoft.EntityFrameworkCore.Metadata.IConventionModel,System.Nullable{MySql.EntityFrameworkCore.Metadata.MySQLValueGenerationStrategy},System.Boolean)">
            <summary>
              Attempts to set the <see cref="T:MySql.EntityFrameworkCore.Metadata.MySQLValueGenerationStrategy" /> to use for properties
              of keys in the model that do not have a strategy explicitly set.
            </summary>
            <param name="model"> The model. </param>
            <param name="value"> The value to set. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLModelExtensions.GetValueGenerationStrategyConfigurationSource(Microsoft.EntityFrameworkCore.Metadata.IConventionModel)">
            <summary>
              Returns the <see cref="T:Microsoft.EntityFrameworkCore.Metadata.ConfigurationSource" /> for the default <see cref="T:MySql.EntityFrameworkCore.Metadata.MySQLValueGenerationStrategy" />.
            </summary>
            <param name="model"> The model. </param>
            <returns> The <see cref="T:Microsoft.EntityFrameworkCore.Metadata.ConfigurationSource" /> for the default <see cref="T:MySql.EntityFrameworkCore.Metadata.MySQLValueGenerationStrategy" />. </returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLModelExtensions.GetCharSet(Microsoft.EntityFrameworkCore.Metadata.IModel)">
            <summary>
              Returns the character set to use as the default for the model or database.
            </summary>
            <param name="model"> The model. </param>
            <returns> The default character set. </returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLModelExtensions.SetCharSet(Microsoft.EntityFrameworkCore.Metadata.IMutableModel,System.String)">
            <summary>
              Attempts to set the character set to use as the default for the model or database.
            </summary>
            <param name="model"> The model. </param>
            <param name="charSet"> The default character set. </param>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLModelExtensions.SetCharSet(Microsoft.EntityFrameworkCore.Metadata.IConventionModel,System.String,System.Boolean)">
            <summary>
              Attempts to set the character set to use as the default for the model or database.
            </summary>
            <param name="model"> The model. </param>
            <param name="charSet"> The default character set. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLModelExtensions.GetCharSetConfigurationSource(Microsoft.EntityFrameworkCore.Metadata.IConventionModel)">
            <summary>
              Returns the <see cref="T:Microsoft.EntityFrameworkCore.Metadata.ConfigurationSource" /> for the default character set of the model or database.
            </summary>
            <param name="model"> The model. </param>
            <returns> The <see cref="T:Microsoft.EntityFrameworkCore.Metadata.ConfigurationSource" /> for the default character set. </returns>
        </member>
        <member name="T:MySql.EntityFrameworkCore.Extensions.MySQLPropertyBuilderExtensions">
            <summary>
              Represents the implementation of MySQL property-builder extensions used in Fluent API.
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLPropertyBuilderExtensions.UseMySQLAutoIncrementColumn(Microsoft.EntityFrameworkCore.Metadata.Builders.PropertyBuilder,System.String)">
            <summary>
              Defines a MySQL auto-increment column.
            </summary>
            <param name="propertyBuilder">Entity property to be set.</param>
            <param name="typeName">MySQL column type as string.</param>
            <returns>Property builder of the auto-increment column.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLPropertyBuilderExtensions.ForMySQLHasDefaultValueSql(Microsoft.EntityFrameworkCore.Metadata.Builders.PropertyBuilder,System.String)">
            <summary>
              Defines the default value expression for a column.
            </summary>
            <param name="propertyBuilder">Entity property to be set.</param>
            <param name="sql">Default value expression.</param>
            <returns>Property builder of a MySQL column with a default value.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLPropertyBuilderExtensions.ForMySQLHasDefaultValue(Microsoft.EntityFrameworkCore.Metadata.Builders.PropertyBuilder,System.Object)">
            <summary>
              Defines the default value for a column.
            </summary>
            <param name="propertyBuilder">Entity property to be set.</param>
            <param name="value">Default value.</param>
            <returns>Property builder of a MySQL column with a default value.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLPropertyBuilderExtensions.ForMySQLHasCharset(Microsoft.EntityFrameworkCore.Metadata.Builders.PropertyBuilder,System.String)">
            <summary>
              Adds the character set to an entity property.
            </summary>
            <param name="propertyBuilder">Property builder.</param>
            <param name="charset">MySQL character set to use.</param>
            <returns>Property builder with a character set.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLPropertyBuilderExtensions.ForMySQLHasCharset(Microsoft.EntityFrameworkCore.Metadata.Builders.EntityTypeBuilder,System.String)">
            <summary>
              Adds the character set to an entity.
            </summary>
            <param name="entityTypeBuilder">Entity type builder.</param>
            <param name="charset">MySQL character set to use.</param>
            <returns>Entity type builder with a character set.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLPropertyBuilderExtensions.ForMySQLHasCollation(Microsoft.EntityFrameworkCore.Metadata.Builders.PropertyBuilder,System.String)">
            <summary>
              Adds the collation to an entity property.
            </summary>
            <param name="propertyBuilder">Property builder.</param>
            <param name="collation">MySQL collation to use.</param>
            <returns>Property builder with a collation.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLPropertyBuilderExtensions.ForMySQLHasCollation(Microsoft.EntityFrameworkCore.Metadata.Builders.EntityTypeBuilder,System.String)">
            <summary>
              Adds the collation to an entity.
            </summary>
            <param name="entityTypeBuilder">Entity type builder.</param>
            <param name="collation">MySQL collation to use.</param>
            <returns>Entity type builder with a collation.</returns>
        </member>
        <member name="T:MySql.EntityFrameworkCore.Extensions.MySQLPropertyExtensions">
            <summary>
              Extension methods for <see cref="T:Microsoft.EntityFrameworkCore.Metadata.IProperty" /> for MySQL metadata.
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLPropertyExtensions.GetValueGenerationStrategy(Microsoft.EntityFrameworkCore.Metadata.IReadOnlyProperty,Microsoft.EntityFrameworkCore.Metadata.StoreObjectIdentifier)">
            <summary>
              <para>
                  Returns the <see cref="T:MySql.EntityFrameworkCore.Metadata.MySQLValueGenerationStrategy" /> to use for the property.
              </para>
              <para>
                  If no strategy is set for the property, then the strategy to use will be taken from the <see cref="T:Microsoft.EntityFrameworkCore.Metadata.IModel" />.
              </para>
            </summary>
            <returns> The strategy, or <see cref="F:MySql.EntityFrameworkCore.Metadata.MySQLValueGenerationStrategy.None" /> if none was set. </returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLPropertyExtensions.IsCompatibleIdentityColumn(Microsoft.EntityFrameworkCore.Metadata.IReadOnlyProperty)">
            <summary>
            Indicates whether the property is compatible with <see cref="F:MySql.EntityFrameworkCore.Metadata.MySQLValueGenerationStrategy.IdentityColumn"/>.
            </summary>
            <param name="property"> The property. </param>
            <returns><see langword="true"/> if compatible; otherwise, <see langword="false"/>.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLPropertyExtensions.IsCompatibleAutoIncrementColumn(Microsoft.EntityFrameworkCore.Metadata.IReadOnlyProperty)">
            <summary>
              Returns a value indicating whether the property is compatible with an `AUTO_INCREMENT` column.
            </summary>
            <param name="property"> The property. </param>
            <returns> <see langword="true"/> if compatible. </returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLPropertyExtensions.GetCharSet(Microsoft.EntityFrameworkCore.Metadata.IReadOnlyProperty)">
            <summary>
            Returns the name of the character set used by the column of the property.
            </summary>
            <param name="property">The property that defines a column's character set.</param>
            <returns>The name of the charset or null, if no explicit charset was set.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLPropertyExtensions.GetMySqlLegacyCharSet(Microsoft.EntityFrameworkCore.Metadata.IReadOnlyProperty)">
            <summary>
            Returns the name of the character set used by the column of the property, defined as part of the column type.
            </summary>
            <param name="property">The property that defines a column's character set.</param>
            <returns>The name of the character set or null, if no explicit character set was set.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLPropertyExtensions.IsCompatibleCurrentTimestampColumn(Microsoft.EntityFrameworkCore.Metadata.IReadOnlyProperty)">
            <summary>
              Returns a value indicating whether the property is compatible with a `CURRENT_TIMESTAMP` column default.
            </summary>
            <param name="property"> The property. </param>
            <returns> <see langword="true"/> if compatible. </returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLPropertyExtensions.IsCompatibleComputedColumn(Microsoft.EntityFrameworkCore.Metadata.IReadOnlyProperty)">
            <summary>
            Indicates whether the property is compatible with <see cref="F:MySql.EntityFrameworkCore.Metadata.MySQLValueGenerationStrategy.ComputedColumn"/>.
            </summary>
            <param name="property"> The property. </param>
            <returns><see langword="true"/> if compatible; otherwise, <see langword="false"/>.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLPropertyExtensions.SetValueGenerationStrategy(Microsoft.EntityFrameworkCore.Metadata.IMutableProperty,System.Nullable{MySql.EntityFrameworkCore.Metadata.MySQLValueGenerationStrategy})">
            <summary>
            Sets the <see cref="T:MySql.EntityFrameworkCore.Metadata.MySQLValueGenerationStrategy" /> to use for the property.
            </summary>
            <param name="property">The property. </param>
            <param name="value">The strategy to use. </param>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLPropertyExtensions.SetValueGenerationStrategy(Microsoft.EntityFrameworkCore.Metadata.IConventionProperty,System.Nullable{MySql.EntityFrameworkCore.Metadata.MySQLValueGenerationStrategy},System.Boolean)">
            <summary>
              Sets the <see cref="T:MySql.EntityFrameworkCore.Metadata.MySQLValueGenerationStrategy" /> to use for the property.
            </summary>
            <param name="property">The property.</param>
            <param name="value">The strategy to use.</param>
            <param name="fromDataAnnotation">Indicates whether the configuration was specified using a data annotation.</param>
            <returns>The configured value.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLPropertyExtensions.GetValueGenerationStrategyConfigurationSource(Microsoft.EntityFrameworkCore.Metadata.IConventionProperty)">
            <summary>
              Returns the <see cref="T:Microsoft.EntityFrameworkCore.Metadata.ConfigurationSource" /> for the <see cref="T:MySql.EntityFrameworkCore.Metadata.MySQLValueGenerationStrategy" />.
            </summary>
            <param name="property">The property.</param>
            <returns>The <see cref="T:Microsoft.EntityFrameworkCore.Metadata.ConfigurationSource" /> for the <see cref="T:MySql.EntityFrameworkCore.Metadata.MySQLValueGenerationStrategy" />.</returns>
        </member>
        <member name="T:MySql.EntityFrameworkCore.Extensions.MySQLServiceCollectionExtensions">
            <summary>
              MySQL extension class for <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLServiceCollectionExtensions.AddMySQLServer``1(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.String,System.Action{MySql.EntityFrameworkCore.Infrastructure.MySQLDbContextOptionsBuilder},System.Action{Microsoft.EntityFrameworkCore.DbContextOptionsBuilder})">
            <summary>
              <para>
                Registers the given Entity Framework <see cref="T:Microsoft.EntityFrameworkCore.DbContext" /> as a service in the <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />
                and configures it to connect to a MySQL database.
              </para>
              <para>
                This method is a shortcut for configuring a <see cref="T:Microsoft.EntityFrameworkCore.DbContext" /> to use MySQL.
              </para>
              <para>
                Use this method when using dependency injection in your application, such as with ASP.NET Core.
                For applications that don't use dependency injection, consider creating <see cref="T:Microsoft.EntityFrameworkCore.DbContext" />
                instances directly with its constructor. The <see cref="M:Microsoft.EntityFrameworkCore.DbContext.OnConfiguring(Microsoft.EntityFrameworkCore.DbContextOptionsBuilder)" /> method can then be
                overridden to configure the MySQL provider and connection string.
              </para>
              <para>
                To configure the <see cref="T:Microsoft.EntityFrameworkCore.DbContextOptions`1" /> for the context, either override the
                <see cref="M:Microsoft.EntityFrameworkCore.DbContext.OnConfiguring(Microsoft.EntityFrameworkCore.DbContextOptionsBuilder)" /> method in your derived context, or supply
                an optional action to configure the <see cref="T:Microsoft.EntityFrameworkCore.DbContextOptions" /> for the context.
              </para>
              <para>
                See <see href="https://aka.ms/efcore-docs-di">Using DbContext with dependency injection</see> for more information.
              </para>
            </summary>
            <typeparam name="TContext">The type of context to be registered.</typeparam>
            <param name="serviceCollection">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> to add services to.</param>
            <param name="connectionString">The connection string of the database to connect to.</param>
            <param name="sqlServerOptionsAction">An optional action to enable additional MySQL server-specific configuration.</param>
            <param name="optionsAction">An optional action to configure the <see cref="T:Microsoft.EntityFrameworkCore.DbContextOptions" /> for the context.</param>
            <returns>The same service collection so that multiple calls can be chained.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLServiceCollectionExtensions.AddEntityFrameworkMySQL(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
              <para>
                Adds the services required by the MySQL database provider for Entity Framework
                to an <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.
              </para>
              <para>
                Warning: Do not call this method by mistake. Instead, it is more likely that you need to call <see cref="M:MySql.EntityFrameworkCore.Extensions.MySQLServiceCollectionExtensions.AddMySQLServer``1(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.String,System.Action{MySql.EntityFrameworkCore.Infrastructure.MySQLDbContextOptionsBuilder},System.Action{Microsoft.EntityFrameworkCore.DbContextOptionsBuilder})" />.
              </para>
              <para>
                Calling this method is no longer necessary when building most applications, including those that
                use dependency injection in ASP.NET or elsewhere.
                It is only needed when building the internal service provider for use with
                the <see cref="M:Microsoft.EntityFrameworkCore.DbContextOptionsBuilder.UseInternalServiceProvider(System.IServiceProvider)" /> method.
                This is not recommend other than for some advanced scenarios.
              </para>
            </summary>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> to add services to.</param>
            <returns>
                The same service collection so that multiple calls can be chained.
            </returns>
        </member>
        <member name="T:MySql.EntityFrameworkCore.Extensions.MySQLTypeBaseExtensions">
            <summary>
              MySQL extension methods for type base.
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLTypeBaseExtensions.GetCharSet(Microsoft.EntityFrameworkCore.Metadata.IReadOnlyTypeBase)">
            <summary>
              Gets the MySQL character set for the table associated with this TypeBase.
            </summary>
            <param name="typeBase"> The TypeBase. </param>
            <returns> The name of the character set. </returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Extensions.MySQLTypeBaseExtensions.GetCollation(Microsoft.EntityFrameworkCore.Metadata.IReadOnlyTypeBase)">
            <summary>
              Gets the MySQL collation for the table associated with this TypeBase.
            </summary>
            <param name="typeBase"> The TypeBase. </param>
            <returns> The name of the collation. </returns>
        </member>
        <member name="T:MySql.EntityFrameworkCore.Infrastructure.Internal.IMySQLOptions">
            <summary>
              Options to set on the provider.
            </summary>
        </member>
        <member name="P:MySql.EntityFrameworkCore.Infrastructure.Internal.IMySQLOptions.CharSet">
            <summary>
            The <see cref="T:MySql.Data.MySqlClient.CharacterSet"/> to be used by the connection.
            </summary>
            <value>The character set.</value>
        </member>
        <member name="P:MySql.EntityFrameworkCore.Infrastructure.Internal.IMySQLOptions.ConnectionSettings">
            <summary>
            The connection settings.
            </summary>
            <value>The connection string builder.</value>
        </member>
        <member name="P:MySql.EntityFrameworkCore.Infrastructure.Internal.IMySQLOptions.SchemaNameTranslator">
            <summary>
            The specified <see cref="T:MySql.EntityFrameworkCore.Infrastructure.MySQLSchemaNameTranslator"/>.
            </summary>
            <value>The SchemaNameTranslator.</value>
        </member>
        <member name="T:MySql.EntityFrameworkCore.Infrastructure.Internal.MySQLOptionsExtension">
            <summary>
              Represents the <see cref="T:Microsoft.EntityFrameworkCore.Infrastructure.RelationalOptionsExtension"/> implementation for MySQL.
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Infrastructure.Internal.MySQLOptionsExtension.#ctor">
            <summary>
            Constructor of <see cref="T:MySql.EntityFrameworkCore.Infrastructure.Internal.MySQLOptionsExtension"/>.
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Infrastructure.Internal.MySQLOptionsExtension.#ctor(MySql.EntityFrameworkCore.Infrastructure.Internal.MySQLOptionsExtension)">
            <summary>
            Creates a <see cref="T:MySql.EntityFrameworkCore.Infrastructure.Internal.MySQLOptionsExtension"/> based on another <see cref="T:MySql.EntityFrameworkCore.Infrastructure.Internal.MySQLOptionsExtension"/>.
            </summary>
            <param name="copyFrom">The <see cref="T:MySql.EntityFrameworkCore.Infrastructure.Internal.MySQLOptionsExtension"/> to copy.</param>
        </member>
        <member name="P:MySql.EntityFrameworkCore.Infrastructure.Internal.MySQLOptionsExtension.Info">
            <summary>
            Information/metadata about the extension.
            </summary>
            <value>The information/metadata.</value>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Infrastructure.Internal.MySQLOptionsExtension.Clone">
            <summary>
            Clones a <see cref="T:MySql.EntityFrameworkCore.Infrastructure.Internal.MySQLOptionsExtension"/> object.
            </summary>
            <returns>A clone of this instance, which can be modified before being returned as immutable.</returns>
        </member>
        <member name="P:MySql.EntityFrameworkCore.Infrastructure.Internal.MySQLOptionsExtension.CharSet">
            <summary>
            The <see cref="T:MySql.Data.MySqlClient.CharacterSet"/> to use.
            </summary>
            <value>The character set.</value>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Infrastructure.Internal.MySQLOptionsExtension.WithCharSet(MySql.Data.MySqlClient.CharacterSet)">
            <summary>
              Returns a copy of the current instance configured with the specified character set.
            </summary>
            <param name="charSet">The <see cref="T:MySql.Data.MySqlClient.CharacterSet"/> to use.</param>
            <returns>A <see cref="T:MySql.EntityFrameworkCore.Infrastructure.Internal.MySQLOptionsExtension"/> object with the specified <paramref name="charSet"/>.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Infrastructure.Internal.MySQLOptionsExtension.ApplyServices(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Adds the services required to make the selected options work.
            </summary>
            <param name="services">The collection to add services to.</param>
        </member>
        <member name="P:MySql.EntityFrameworkCore.Infrastructure.Internal.MySQLOptionsExtension.ExtensionInfo.IsDatabaseProvider">
            <inheritdoc />
        </member>
        <member name="P:MySql.EntityFrameworkCore.Infrastructure.Internal.MySQLOptionsExtension.ExtensionInfo.LogFragment">
            <inheritdoc />
        </member>
        <member name="M:MySql.EntityFrameworkCore.Infrastructure.Internal.MySQLOptionsExtension.ExtensionInfo.GetServiceProviderHashCode">
            <inheritdoc />
        </member>
        <member name="M:MySql.EntityFrameworkCore.Infrastructure.Internal.MySQLOptionsExtension.ExtensionInfo.PopulateDebugInfo(System.Collections.Generic.IDictionary{System.String,System.String})">
            <inheritdoc />
        </member>
        <member name="T:MySql.EntityFrameworkCore.Infrastructure.MySQLDbContextOptionsBuilder">
            <summary>
            Represents the <see cref="T:Microsoft.EntityFrameworkCore.Infrastructure.RelationalDbContextOptionsBuilder`2" /> implementation for MySQL.
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Infrastructure.MySQLDbContextOptionsBuilder.#ctor(Microsoft.EntityFrameworkCore.DbContextOptionsBuilder)">
            <summary>
              Initializes a new instance of the <see cref="T:MySql.EntityFrameworkCore.Infrastructure.MySQLDbContextOptionsBuilder" /> class.
            </summary>
            <param name="optionsBuilder"> The options builder. </param>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Infrastructure.MySQLDbContextOptionsBuilder.EnableRetryOnFailure">
            <summary>
              <para>
                Configures the context to use the default retrying <see cref="T:Microsoft.EntityFrameworkCore.Storage.IExecutionStrategy" />.
              </para>
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Infrastructure.MySQLDbContextOptionsBuilder.EnableRetryOnFailure(System.Int32)">
            <summary>
              <para>
                Configures the context to use the default retrying <see cref="T:Microsoft.EntityFrameworkCore.Storage.IExecutionStrategy" />.
              </para>
            </summary>
            <param name="maxRetryCount">The maximum number of retry attempts.</param>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Infrastructure.MySQLDbContextOptionsBuilder.EnableRetryOnFailure(System.Int32,System.TimeSpan,System.Collections.Generic.ICollection{System.Int32})">
            <summary>
              <para>
                Configures the context to use the default retrying <see cref="T:Microsoft.EntityFrameworkCore.Storage.IExecutionStrategy" />.
              </para>
            </summary>
            <param name="maxRetryCount">The maximum number of retry attempts.</param>
            <param name="maxRetryDelay">The maximum delay between retries.</param>
            <param name="errorNumbersToAdd">Additional SQL error numbers that should be considered transient.</param>
        </member>
        <member name="T:MySql.EntityFrameworkCore.Infrastructure.MySQLSchemaNameTranslator">
            <summary>
              Translates the specified schema and object to an output object named for the schema being used.
            </summary>
            <param name="schemaName">schema name</param>
            <param name="objectName">object name</param>
        </member>
        <member name="T:MySql.EntityFrameworkCore.Infrastructure.MySqlSchemaBehavior">
            <summary>
              Represents the behavior of the schema.
            </summary>
        </member>
        <member name="F:MySql.EntityFrameworkCore.Infrastructure.MySqlSchemaBehavior.Throw">
            <summary>
              Throws an exception if a schema is being used. All specified translator delegates are ignored.
              This is the default.
            </summary>
        </member>
        <member name="F:MySql.EntityFrameworkCore.Infrastructure.MySqlSchemaBehavior.Ignore">
            <summary>
              Silently ignores any schema definitions. All specified translator delegates are ignored.
            </summary>
        </member>
        <member name="F:MySql.EntityFrameworkCore.Infrastructure.MySqlSchemaBehavior.Translate">
            <summary>
              Uses the specified translator delegate to translate an input schema and object name to
              an output object named for the schema being used.
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Internal.MySQLModelValidator.Validate(Microsoft.EntityFrameworkCore.Metadata.IModel,Microsoft.EntityFrameworkCore.Diagnostics.IDiagnosticsLogger{Microsoft.EntityFrameworkCore.DbLoggerCategory.Model.Validation})">
            <inheritdoc/>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Internal.MySQLRetryingExecutionStrategy.#ctor(Microsoft.EntityFrameworkCore.DbContext)">
            <summary>
              Creates a new instance of <see cref="T:MySql.EntityFrameworkCore.Internal.MySQLRetryingExecutionStrategy" />.
            </summary>
            <param name="context"> The context on which the operations will be invoked. </param>
            <remarks>
              The default retry limit is 6, which means that the total amount of time spent before failing is about a minute.
            </remarks>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Internal.MySQLRetryingExecutionStrategy.#ctor(Microsoft.EntityFrameworkCore.Storage.ExecutionStrategyDependencies)">
            <summary>
              Creates a new instance of <see cref="T:MySql.EntityFrameworkCore.Internal.MySQLRetryingExecutionStrategy" />.
            </summary>
            <param name="dependencies"> Parameter object containing service dependencies. </param>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Internal.MySQLRetryingExecutionStrategy.#ctor(Microsoft.EntityFrameworkCore.DbContext,System.Int32)">
            <summary>
              Creates a new instance of <see cref="T:MySql.EntityFrameworkCore.Internal.MySQLRetryingExecutionStrategy" />.
            </summary>
            <param name="context"> The context used to invoke the operations. </param>
            <param name="maxRetryCount"> The maximum number of retry attempts. </param>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Internal.MySQLRetryingExecutionStrategy.#ctor(Microsoft.EntityFrameworkCore.Storage.ExecutionStrategyDependencies,System.Int32)">
            <summary>
              Creates a new instance of <see cref="T:MySql.EntityFrameworkCore.Internal.MySQLRetryingExecutionStrategy" />.
            </summary>
            <param name="dependencies"> Parameter object containing service dependencies. </param>
            <param name="maxRetryCount"> The maximum number of retry attempts. </param>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Internal.MySQLRetryingExecutionStrategy.#ctor(Microsoft.EntityFrameworkCore.DbContext,System.Int32,System.TimeSpan,System.Collections.Generic.ICollection{System.Int32})">
            <summary>
              Creates a new instance of <see cref="T:MySql.EntityFrameworkCore.Internal.MySQLRetryingExecutionStrategy" />.
            </summary>
            <param name="context"> The context used to invoke the operations. </param>
            <param name="maxRetryCount"> The maximum number of retry attempts. </param>
            <param name="maxRetryDelay"> The maximum delay between retries. </param>
            <param name="errorNumbersToAdd"> Additional SQL error numbers that should be considered transient. </param>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Internal.MySQLRetryingExecutionStrategy.#ctor(Microsoft.EntityFrameworkCore.Storage.ExecutionStrategyDependencies,System.Int32,System.TimeSpan,System.Collections.Generic.ICollection{System.Int32})">
            <summary>
              Creates a new instance of <see cref="T:MySql.EntityFrameworkCore.Internal.MySQLRetryingExecutionStrategy" />.
            </summary>
            <param name="dependencies"> Parameter object containing service dependencies. </param>
            <param name="maxRetryCount"> The maximum number of retry attempts. </param>
            <param name="maxRetryDelay"> The maximum delay between retries. </param>
            <param name="errorNumbersToAdd"> Additional SQL error numbers that should be considered transient. </param>
        </member>
        <member name="T:MySql.EntityFrameworkCore.Metadata.Conventions.MySqlCharsetAttributeConvention">
            <summary>
              Sets a character-set attribute for a property.
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Metadata.Conventions.MySqlCharsetAttributeConvention.#ctor(Microsoft.EntityFrameworkCore.Metadata.Conventions.Infrastructure.ProviderConventionSetBuilderDependencies)">
            <summary>
              Creates a new instance of <see cref="T:Microsoft.EntityFrameworkCore.Metadata.Conventions.PropertyAttributeConventionBase`1" />.
            </summary>
            <param name="dependencies"> Parameter object containing dependencies for this convention. </param>
        </member>
        <member name="T:MySql.EntityFrameworkCore.Metadata.Conventions.MySqlCollationAttributeConvention">
            <summary>
              Represents a collation attribute for a property.
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Metadata.Conventions.MySqlCollationAttributeConvention.#ctor(Microsoft.EntityFrameworkCore.Metadata.Conventions.Infrastructure.ProviderConventionSetBuilderDependencies)">
            <summary>
              Creates a new instance of <see cref="T:Microsoft.EntityFrameworkCore.Metadata.Conventions.PropertyAttributeConventionBase`1" />.
            </summary>
            <param name="dependencies"> Parameter object containing dependencies for this convention. </param>
        </member>
        <member name="T:MySql.EntityFrameworkCore.Metadata.Conventions.MySQLConventionSetBuilder">
            <summary>
              <para>
                A convention set builder for MySQL.
              </para>
              <para>
                The service lifetime is <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Scoped" /> and multiple registrations
                are allowed. This means that each <see cref="T:Microsoft.EntityFrameworkCore.DbContext" /> instance uses its own
                set of instances of this service.
                The implementations may depend on other services registered with any lifetime.
                The implementations do not need to be thread-safe.
              </para>
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Metadata.Conventions.MySQLConventionSetBuilder.CreateConventionSet">
            <summary>
              Builds and returns the convention set for MySQL.
            </summary>
            <returns> The convention set for MySQL. </returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Metadata.Conventions.MySQLConventionSetBuilder.Build">
            <summary>
            <para>
              Call this method to build a <see cref="T:Microsoft.EntityFrameworkCore.Metadata.Conventions.ConventionSet" /> for MySQL when using
              the <see cref="T:Microsoft.EntityFrameworkCore.ModelBuilder" /> outside of <see cref="M:Microsoft.EntityFrameworkCore.DbContext.OnModelCreating(Microsoft.EntityFrameworkCore.ModelBuilder)" />.
            </para>
            <para>
              Note that it is unusual to use this method.
              Consider using <see cref="T:Microsoft.EntityFrameworkCore.DbContext" /> in the normal way instead.
            </para>
            </summary>
            <returns> The convention set. </returns>
        </member>
        <member name="T:MySql.EntityFrameworkCore.Metadata.Conventions.MySqlEntityCharsetAttributeConvention">
            <summary>
              Represents a character-set attribute for a Type.
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Metadata.Conventions.MySqlEntityCharsetAttributeConvention.#ctor(Microsoft.EntityFrameworkCore.Metadata.Conventions.Infrastructure.ProviderConventionSetBuilderDependencies)">
            <summary>
              Creates a new instance of <see cref="T:Microsoft.EntityFrameworkCore.Metadata.Conventions.PropertyAttributeConventionBase`1" />.
            </summary>
            <param name="dependencies"> Parameter object containing dependencies for this convention. </param>
        </member>
        <member name="T:MySql.EntityFrameworkCore.Metadata.Conventions.MySqlEntityCollationAttributeConvention">
            <summary>
              Represents a collation attribute for an Type.
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Metadata.Conventions.MySqlEntityCollationAttributeConvention.#ctor(Microsoft.EntityFrameworkCore.Metadata.Conventions.Infrastructure.ProviderConventionSetBuilderDependencies)">
            <summary>
              Creates a new instance of <see cref="T:Microsoft.EntityFrameworkCore.Metadata.Conventions.TypeAttributeConventionBase`1" />.
            </summary>
            <param name="dependencies"> Parameter object containing dependencies for this convention. </param>
        </member>
        <member name="T:MySql.EntityFrameworkCore.Metadata.Conventions.MySQLValueGenerationConvention">
            <summary>
              A convention that configures store value generation as <see cref="F:Microsoft.EntityFrameworkCore.Metadata.ValueGenerated.OnAdd"/> on properties that are
              part of the primary key and not part of any foreign keys, are configured to have a database default value, or are 
              configured to use a <see cref="T:MySql.EntityFrameworkCore.Metadata.MySQLValueGenerationStrategy"/>.
              It also configures properties as <see cref="F:Microsoft.EntityFrameworkCore.Metadata.ValueGenerated.OnAddOrUpdate"/> if they are configured as computed columns.
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Metadata.Conventions.MySQLValueGenerationConvention.#ctor(Microsoft.EntityFrameworkCore.Metadata.Conventions.Infrastructure.ProviderConventionSetBuilderDependencies,Microsoft.EntityFrameworkCore.Metadata.Conventions.Infrastructure.RelationalConventionSetBuilderDependencies)">
            <summary>
            Creates a new instance of <see cref="T:MySql.EntityFrameworkCore.Metadata.Conventions.MySQLValueGenerationConvention" />.
            </summary>
            <param name="dependencies"> Parameter object containing dependencies for this convention. </param>
            <param name="relationalDependencies">  Parameter object containing relational dependencies for this convention. </param>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Metadata.Conventions.MySQLValueGenerationConvention.ProcessPropertyAnnotationChanged(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionPropertyBuilder,System.String,Microsoft.EntityFrameworkCore.Metadata.IConventionAnnotation,Microsoft.EntityFrameworkCore.Metadata.IConventionAnnotation,Microsoft.EntityFrameworkCore.Metadata.Conventions.IConventionContext{Microsoft.EntityFrameworkCore.Metadata.IConventionAnnotation})">
            <summary>
            Called after an annotation is changed on a property.
            </summary>
            <param name="propertyBuilder"> The builder for the property. </param>
            <param name="name"> The annotation name. </param>
            <param name="annotation"> The new annotation. </param>
            <param name="oldAnnotation"> The old annotation.  </param>
            <param name="context"> Additional information associated with convention execution. </param>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Metadata.Conventions.MySQLValueGenerationConvention.GetValueGenerated(Microsoft.EntityFrameworkCore.Metadata.IConventionProperty)">
            <summary>
            Indicates the store value generation strategy to set for the given property.
            </summary>
            <param name="property"> The property. </param>
            <returns> The store value generation strategy to set for the given property. </returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Metadata.Conventions.MySQLValueGenerationConvention.GetValueGenerated(Microsoft.EntityFrameworkCore.Metadata.IReadOnlyProperty,Microsoft.EntityFrameworkCore.Metadata.StoreObjectIdentifier@)">
            <summary>
              Returns the store value generation strategy to set for the given property.
            </summary>
            <param name="property"> The property. </param>
            <param name="storeObject"> The identifier of the store object. </param>
            <returns> The store value generation strategy to set for the given property. </returns>
        </member>
        <member name="T:MySql.EntityFrameworkCore.Metadata.Internal.MySQLAnnotationNames">
            <summary>
              Provides names for MySQL annotations.
            </summary>
        </member>
        <member name="T:MySql.EntityFrameworkCore.Metadata.MySQLValueGenerationStrategy">
            <summary>
              Defines two strategies to use across the EF Core stack when generating key values
              from MySQL database columns.
            </summary>
        </member>
        <member name="F:MySql.EntityFrameworkCore.Metadata.MySQLValueGenerationStrategy.None">
            <summary>
              No specific MySQL strategy.
            </summary>
        </member>
        <member name="F:MySql.EntityFrameworkCore.Metadata.MySQLValueGenerationStrategy.IdentityColumn">
            <summary>
              A pattern that uses a standard MySQL <c>Identity</c> column in the same way as EF6 and previous frameworks.
            </summary>
        </member>
        <member name="F:MySql.EntityFrameworkCore.Metadata.MySQLValueGenerationStrategy.ComputedColumn">
            <summary>
              Values of a generated column are computed from an expression included in the column definition.
            </summary>
        </member>
        <member name="T:MySql.EntityFrameworkCore.Migrations.Internal.MySQLHistoryRepository">
            <summary>
              <para>
                The service lifetime is <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Scoped" />. This means that each
                <see cref="T:Microsoft.EntityFrameworkCore.DbContext" /> instance will use its own instance of this service.
                The implementation may depend on other services registered with any lifetime.
                The implementation does not need to be thread-safe.
              </para>
            </summary>
        </member>
        <member name="T:MySql.EntityFrameworkCore.Migrations.MySQLMigrationsSqlGenerator">
            <summary>
              MigrationSqlGenerator implementation for MySQL
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Migrations.MySQLMigrationsSqlGenerator.ColumnDefinition(Microsoft.EntityFrameworkCore.Migrations.Operations.AddColumnOperation,Microsoft.EntityFrameworkCore.Metadata.IModel,Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder)">
            <summary>
              Generates a SQL fragment for a column definition in an <see cref="T:Microsoft.EntityFrameworkCore.Migrations.Operations.AddColumnOperation" />.
            </summary>
            <param name="operation"> The operation. </param>
            <param name="model"> The target model, which may be <c>null</c> if the operations exist without a model. </param>
            <param name="builder"> The command builder to use to add the SQL fragment. </param>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Migrations.MySQLMigrationsSqlGenerator.ColumnDefinition(System.String,System.String,System.String,Microsoft.EntityFrameworkCore.Migrations.Operations.ColumnOperation,Microsoft.EntityFrameworkCore.Metadata.IModel,Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder)">
            <summary>
              Generates a SQL fragment for a column definition for the given column metadata.
            </summary>
            <param name="schema"> The schema that contains the table, or <c>null</c> to use the default schema. </param>
            <param name="table"> The table that contains the column. </param>
            <param name="name"> The column name. </param>
            <param name="operation"> The column metadata. </param>
            <param name="model"> The target model, which may be <c>null</c> if the operations exist without a model. </param>
            <param name="builder"> The command builder to use to add the SQL fragment. </param>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Migrations.MySQLMigrationsSqlGenerator.DefaultValue(System.Object,System.String,System.String,Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder)">
            <summary>
              Generates a SQL fragment for the default constraint of a column.
            </summary>
            <param name="defaultValue"> The default value for the column. </param>
            <param name="defaultValueSql"> The SQL expression to use for the column's default constraint. </param>
            <param name="builder"> The command builder to use to add the SQL fragment. </param>
            <param name="columnType"> Store/database type of the column. </param>
        </member>
        <member name="T:MySql.EntityFrameworkCore.Migrations.Operations.MySQLCreateDatabaseOperation">
            <summary>
            Creates a database operation class for migrations.
            </summary>
        </member>
        <member name="T:MySql.EntityFrameworkCore.Migrations.Operations.MySQLDropDatabaseOperation">
            <summary>
              Drops database operation for migrations.
            </summary>
        </member>
        <member name="P:MySql.EntityFrameworkCore.Migrations.Operations.MySQLDropDatabaseOperation.Name">
            <summary>
              The name of the database.
            </summary>
        </member>
        <member name="T:MySql.EntityFrameworkCore.Migrations.Operations.MySQLDropPrimaryKeyAndRecreateForeignKeysOperation">
            <summary>
              A migration operation for dropping a primary key and recreating foreign keys.
            </summary>
        </member>
        <member name="P:MySql.EntityFrameworkCore.Migrations.Operations.MySQLDropPrimaryKeyAndRecreateForeignKeysOperation.RecreateForeignKeys">
            <summary>
              Recreate all foreign keys or not.
            </summary>
        </member>
        <member name="T:MySql.EntityFrameworkCore.Migrations.Operations.MySQLDropUniqueConstraintAndRecreateForeignKeysOperation">
            <summary>
              A migration operation for dropping a unique constraint and recreating foreign keys.
            </summary>
        </member>
        <member name="P:MySql.EntityFrameworkCore.Migrations.Operations.MySQLDropUniqueConstraintAndRecreateForeignKeysOperation.RecreateForeignKeys">
            <summary>
              Recreate all foreign keys or not.
            </summary>
        </member>
        <member name="T:MySql.EntityFrameworkCore.Properties.MySQLStrings">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:MySql.EntityFrameworkCore.Properties.MySQLStrings.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:MySql.EntityFrameworkCore.Properties.MySQLStrings.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:MySql.EntityFrameworkCore.Properties.MySQLStrings.ExpressionTypeMismatch">
            <summary>
              Looks up a localized string similar to The specified expression does not have the correct Type..
            </summary>
        </member>
        <member name="P:MySql.EntityFrameworkCore.Properties.MySQLStrings.InvalidTableToIncludeInScaffolding">
            <summary>
              Looks up a localized string similar to The specified table &apos;{0}&apos; is not valid..
            </summary>
        </member>
        <member name="P:MySql.EntityFrameworkCore.Properties.MySQLStrings.NoInitialCatalog">
            <summary>
              Looks up a localized string similar to The database name could not be determined. To use EnsureDeleted, the connection string must specify Initial Catalog..
            </summary>
        </member>
        <member name="P:MySql.EntityFrameworkCore.Properties.MySQLStrings.SaveChangesFailed">
            <summary>
              Looks up a localized string similar to Could not save changes. Please configure your entity type accordingly..
            </summary>
        </member>
        <member name="P:MySql.EntityFrameworkCore.Properties.MySQLStrings.TransientExceptionDetected">
            <summary>
              Looks up a localized string similar to An exception has been raised that is likely due to a transient failure. Consider enabling transient error resiliency by adding &apos;EnableRetryOnFailure()&apos; to the &apos;UseMySql&apos; call..
            </summary>
        </member>
        <member name="P:MySql.EntityFrameworkCore.Properties.MySQLStrings.UnqualifiedDataType">
            <summary>
              Looks up a localized string similar to Missing length for data type &apos;{0}&apos;..
            </summary>
        </member>
        <member name="P:MySql.EntityFrameworkCore.Properties.MySQLStrings.WrongComputedType">
            <summary>
              Looks up a localized string similar to The specified type is invalid for computed value generation  (&apos;{0}&apos;  &apos;{1}&apos;  &apos;{2}&apos;). The expected types are DateTime, and DateTimeOffset..
            </summary>
        </member>
        <member name="P:MySql.EntityFrameworkCore.Properties.MySQLStrings.WrongIdentityType">
            <summary>
              Looks up a localized string similar to The specified type is invalid for identity value generation  (&apos;{0}&apos;  &apos;{1}&apos;  &apos;{2}&apos;). The expected types are int, DateTime, and DateTimeOffset..
            </summary>
        </member>
        <member name="T:MySql.EntityFrameworkCore.Query.Expressions.Internal.MySQLCollateExpression">
            <summary>
              An expression that explicitly specifies the collation of a string value.
            </summary>
        </member>
        <member name="P:MySql.EntityFrameworkCore.Query.Expressions.Internal.MySQLCollateExpression.ValueExpression">
            <summary>
              The expression for which a collation is being specified.
            </summary>
        </member>
        <member name="P:MySql.EntityFrameworkCore.Query.Expressions.Internal.MySQLCollateExpression.Charset">
            <summary>
              The character set that the string is being converted to.
            </summary>
        </member>
        <member name="P:MySql.EntityFrameworkCore.Query.Expressions.Internal.MySQLCollateExpression.Collation">
            <summary>
              The collation that the string is being converted to.
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Query.Expressions.Internal.MySQLCollateExpression.Accept(System.Linq.Expressions.ExpressionVisitor)">
            <summary>
              Dispatches to the specific visit method for this node type.
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Query.Expressions.Internal.MySQLCollateExpression.VisitChildren(System.Linq.Expressions.ExpressionVisitor)">
            <summary>
              Reduces the node and then calls the <see cref="M:System.Linq.Expressions.ExpressionVisitor.Visit(System.Linq.Expressions.Expression)" /> method passing the
              reduced expression.
              Throws an exception if the node isn't reducible.
            </summary>
            <param name="visitor"> An instance of <see cref="T:System.Linq.Expressions.ExpressionVisitor" />. </param>
            <returns> The expression being visited, or an expression which should replace it in the tree. </returns>
            <remarks>
              Override this method to provide logic to walk the node's children.
              A typical implementation will call visitor.Visit on each of its
              children, and if any of them change, should return a new copy of
              itself with the modified children.
            </remarks>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Query.Expressions.Internal.MySQLCollateExpression.Equals(System.Object)">
            <summary>
              Tests if this object is considered equal to another.
            </summary>
            <param name="obj"> The object to compare with the current object. </param>
            <returns>
              <see langword="true"/> if the objects are considered equal; otherwise, <see langword="false"/>.
            </returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Query.Expressions.Internal.MySQLCollateExpression.GetHashCode">
            <summary>
              Returns a hash code for this object.
            </summary>
            <returns>
              A hash code for this object.
            </returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Query.Expressions.Internal.MySQLCollateExpression.ToString">
            <summary>
              Creates a <see cref="T:System.String" /> representation of the Expression.
            </summary>
            <returns>A <see cref="T:System.String" /> representation of the Expression.</returns>
        </member>
        <member name="T:MySql.EntityFrameworkCore.Query.Expressions.Internal.MySQLColumnAliasReferenceExpression">
            <summary>
            Enables referencing an alias from within the same SELECT statement, such as in a HAVING clause.
            </summary>
        </member>
        <member name="P:MySql.EntityFrameworkCore.Query.Expressions.Internal.MySQLComplexFunctionArgumentExpression.ArgumentParts">
            <summary>
              The arguments parts.
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Query.Expressions.Internal.MySQLComplexFunctionArgumentExpression.Accept(System.Linq.Expressions.ExpressionVisitor)">
            <summary>
              Dispatches to the specific visit method for this node type.
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Query.Expressions.Internal.MySQLComplexFunctionArgumentExpression.GetHashCode">
            <summary>
              Returns a hash code for this object.
            </summary>
            <returns>
              A hash code for this object.
            </returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Query.Expressions.Internal.MySQLComplexFunctionArgumentExpression.ToString">
            <summary>
              Creates a <see cref="T:System.String" /> representation of the Expression.
            </summary>
            <returns>A <see cref="T:System.String" /> representation of the Expression.</returns>
        </member>
        <member name="T:MySql.EntityFrameworkCore.Query.Expressions.Internal.MySQLJsonArrayIndexExpression">
            <summary>
              Represents a MySQL JSON array index (x[y]).
            </summary>
        </member>
        <member name="T:MySql.EntityFrameworkCore.Query.Expressions.Internal.MySQLJsonTraversalExpression">
            <summary>
            Represents a MySQL JSON operator traversing a JSON document with a path (x->y or x->>y)
            </summary>
        </member>
        <member name="P:MySql.EntityFrameworkCore.Query.Expressions.Internal.MySQLJsonTraversalExpression.Expression">
            <summary>
            The JSON column.
            </summary>
        </member>
        <member name="P:MySql.EntityFrameworkCore.Query.Expressions.Internal.MySQLJsonTraversalExpression.Path">
            <summary>
            The path inside the JSON column.
            </summary>
        </member>
        <member name="P:MySql.EntityFrameworkCore.Query.Expressions.Internal.MySQLJsonTraversalExpression.ReturnsText">
            <summary>
            Whether the text-returning operator (x->>y) or the object-returning operator (x->y) is used.
            </summary>
        </member>
        <member name="T:MySql.EntityFrameworkCore.Query.Internal.MySQLCompiledQueryCacheKeyGenerator">
            <summary>
              <para>
                The service lifetime is <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Scoped" />. This means that each
                <see cref="T:Microsoft.EntityFrameworkCore.DbContext" /> instance uses its own instance of this service.
                The implementation may depend on other services registered with any lifetime.
                The implementation does not need to be thread-safe.
              </para>
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Query.Internal.MySQLCompiledQueryCacheKeyGenerator.GenerateCacheKey(System.Linq.Expressions.Expression,System.Boolean)">
            <inheritdoc/>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Query.Internal.MySQLMathMethodTranslator.Translate(Microsoft.EntityFrameworkCore.Query.SqlExpressions.SqlExpression,System.Reflection.MethodInfo,System.Collections.Generic.IReadOnlyList{Microsoft.EntityFrameworkCore.Query.SqlExpressions.SqlExpression},Microsoft.EntityFrameworkCore.Diagnostics.IDiagnosticsLogger{Microsoft.EntityFrameworkCore.DbLoggerCategory.Query})">
            <inheritdoc />
        </member>
        <member name="M:MySql.EntityFrameworkCore.Query.Internal.MySQLParameterBasedSqlProcessor.ProcessSqlNullability(System.Linq.Expressions.Expression,System.Collections.Generic.IReadOnlyDictionary{System.String,System.Object},System.Boolean@)">
            <inheritdoc />
        </member>
        <member name="M:MySql.EntityFrameworkCore.Query.Internal.MySQLSqlNullabilityProcessor.#ctor(Microsoft.EntityFrameworkCore.Query.RelationalParameterBasedSqlProcessorDependencies,System.Boolean)">
            <summary>
            Creates a new instance of the <see cref="T:MySql.EntityFrameworkCore.Query.Internal.MySQLSqlNullabilityProcessor" />.
            </summary>
            <param name="dependencies">Parameter object containing dependencies for this class.</param>
            <param name="useRelationalNulls">A bool value indicating whether relational null semantics are in use.</param>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Query.Internal.MySQLSqlNullabilityProcessor.VisitCustomSqlExpression(Microsoft.EntityFrameworkCore.Query.SqlExpressions.SqlExpression,System.Boolean,System.Boolean@)">
            <inheritdoc />
        </member>
        <member name="M:MySql.EntityFrameworkCore.Query.Internal.MySQLSqlNullabilityProcessor.VisitBinary(MySql.EntityFrameworkCore.Query.Expressions.Internal.MySQLBinaryExpression,System.Boolean,System.Boolean@)">
            <summary>
            Visits a <see cref="T:MySql.EntityFrameworkCore.Query.Expressions.Internal.MySQLBinaryExpression" /> and computes its nullability.
            </summary>
            <param name="binaryExpression">A <see cref="T:MySql.EntityFrameworkCore.Query.Expressions.Internal.MySQLBinaryExpression" /> expression to visit.</param>
            <param name="allowOptimizedExpansion">A bool value indicating if optimized expansion which considers null value as false value is allowed.</param>
            <param name="nullable">A bool value indicating whether the sql expression is nullable.</param>
            <returns>An optimized sql expression.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Query.Internal.MySQLSqlNullabilityProcessor.VisitCollate(MySql.EntityFrameworkCore.Query.Expressions.Internal.MySQLCollateExpression,System.Boolean,System.Boolean@)">
            <summary>
            Visits a <see cref="T:MySql.EntityFrameworkCore.Query.Expressions.Internal.MySQLCollateExpression" /> and computes its nullability.
            </summary>
            <param name="collateExpression">A <see cref="T:MySql.EntityFrameworkCore.Query.Expressions.Internal.MySQLCollateExpression" /> expression to visit.</param>
            <param name="allowOptimizedExpansion">A bool value indicating if optimized expansion which considers null value as false value is allowed.</param>
            <param name="nullable">A bool value indicating whether the sql expression is nullable.</param>
            <returns>An optimized sql expression.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Query.Internal.MySQLSqlNullabilityProcessor.VisitComplexFunctionArgument(MySql.EntityFrameworkCore.Query.Expressions.Internal.MySQLComplexFunctionArgumentExpression,System.Boolean,System.Boolean@)">
            <summary>
            Visits a <see cref="T:MySql.EntityFrameworkCore.Query.Expressions.Internal.MySQLComplexFunctionArgumentExpression" /> and computes its nullability.
            </summary>
            <param name="complexFunctionArgumentExpression">A <see cref="T:MySql.EntityFrameworkCore.Query.Expressions.Internal.MySQLComplexFunctionArgumentExpression" /> expression to visit.</param>
            <param name="allowOptimizedExpansion">A bool value indicating if optimized expansion which considers null value as false value is allowed.</param>
            <param name="nullable">A bool value indicating whether the sql expression is nullable.</param>
            <returns>An optimized sql expression.</returns>
        </member>
        <member name="T:MySql.EntityFrameworkCore.Query.MySQLJsonString">
            <summary>
              This class can be used to represent a string that contains valid JSON data. 
              To mark a string as containing JSON data, just cast the string to `MySQLJsonString`.
            </summary>
        </member>
        <member name="T:MySql.EntityFrameworkCore.Query.MySQLQueryGeneratorFactory">
            <summary>
            Implementation for QuerySqlGeneratorFactoryBase
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Scaffolding.Internal.MySQLCodeGenerator.GenerateUseProvider(System.String,Microsoft.EntityFrameworkCore.Design.MethodCallCodeFragment)">
            <inheritdoc/>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Scaffolding.Internal.MySQLDatabaseModelFactory.Create(System.String,Microsoft.EntityFrameworkCore.Scaffolding.DatabaseModelFactoryOptions)">
            <inheritdoc/>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Storage.Internal.MySQLBoolTypeMapping.Clone(Microsoft.EntityFrameworkCore.Storage.RelationalTypeMapping.RelationalTypeMappingParameters)">
            <summary>
              Creates a copy of this mapping.
            </summary>
            <param name="parameters"> The parameters for this mapping. </param>
            <returns> The newly created mapping. </returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Storage.Internal.MySQLBoolTypeMapping.GenerateNonNullSqlLiteral(System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Storage.Internal.MySQLByteArrayTypeMapping.Clone(Microsoft.EntityFrameworkCore.Storage.RelationalTypeMapping.RelationalTypeMappingParameters)">
            <summary>
              Creates a copy of this mapping.
            </summary>
            <param name="parameters"> The parameters for this mapping. </param>
            <returns> The newly created mapping. </returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Storage.Internal.MySQLByteArrayTypeMapping.ConfigureParameter(System.Data.Common.DbParameter)">
            <inheritdoc/>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Storage.Internal.MySQLByteArrayTypeMapping.GenerateNonNullSqlLiteral(System.Object)">
            <summary>
              Generates the SQL representation of a literal value.
            </summary>
            <param name="value">The literal value.</param>
            <returns>
              The generated string.
            </returns>
        </member>
        <member name="T:MySql.EntityFrameworkCore.Storage.Internal.MySQLDatabaseCreator">
            <summary>
            Relational Database creator implementation in MySQL
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Storage.Internal.MySQLDatabaseCreator.Delete">
            <inheritdoc/>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Storage.Internal.MySQLDatabaseCreator.Exists">
            <inheritdoc/>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Storage.Internal.MySQLDatabaseCreator.ExistsAsync(System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Storage.Internal.MySQLDatabaseCreator.HasTables">
            <inheritdoc/>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Storage.Internal.MySQLDatabaseCreator.HasTablesAsync(System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="T:MySql.EntityFrameworkCore.Storage.Internal.MySQLDateTimeOffsetTypeMapping">
            <summary>
              This is an internal API that supports the Entity Framework Core infrastructure and not subject to
              the same compatibility standards as public APIs. It may be changed or removed without notice in
              any release. You should only use it directly in your code with extreme caution and knowing that
              doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Storage.Internal.MySQLDateTimeOffsetTypeMapping.#ctor(System.String,System.Nullable{System.Data.DbType},System.Nullable{System.Int32},Microsoft.EntityFrameworkCore.Storage.StoreTypePostfix)">
            <summary>
              This is an internal API that supports the Entity Framework Core infrastructure and not subject to
              the same compatibility standards as public APIs. It may be changed or removed without notice in
              any release. You should only use it directly in your code with extreme caution and knowing that
              doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Storage.Internal.MySQLDateTimeOffsetTypeMapping.#ctor(Microsoft.EntityFrameworkCore.Storage.RelationalTypeMapping.RelationalTypeMappingParameters)">
            <summary>
              Initializes a new instance of the <see cref="T:Microsoft.EntityFrameworkCore.Storage.DateTimeOffsetTypeMapping" /> class.
            </summary>
            <param name="parameters">Parameter object for <see cref="T:Microsoft.EntityFrameworkCore.Storage.RelationalTypeMapping" />.</param>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Storage.Internal.MySQLDateTimeOffsetTypeMapping.Clone(Microsoft.EntityFrameworkCore.Storage.RelationalTypeMapping.RelationalTypeMappingParameters)">
            <summary>
              Creates a copy of this mapping.
            </summary>
            <param name="parameters">The parameters for this mapping.</param>
            <returns>The newly created mapping.</returns>
        </member>
        <member name="P:MySql.EntityFrameworkCore.Storage.Internal.MySQLDateTimeOffsetTypeMapping.SqlLiteralFormatString">
            <summary>
              Gets the string format to be used to generate SQL literals of this type.
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Storage.Internal.MySQLDateTimeTypeMapping.Clone(Microsoft.EntityFrameworkCore.Storage.RelationalTypeMapping.RelationalTypeMappingParameters)">
            <summary>
              Creates a copy of this mapping.
            </summary>
            <param name="parameters"> The parameters for this mapping. </param>
            <returns> The newly created mapping. </returns>
        </member>
        <member name="T:MySql.EntityFrameworkCore.Storage.Internal.MySQLDateTypeMapping">
            <summary>
              <para>
                Represents the mapping between a .NET <see cref="T:System.DateTime" /> type and a database type.
              </para>
              <para>
                This type is typically used by database providers (and other extensions). It is generally
                not used in application code.
              </para>
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Storage.Internal.MySQLDateTypeMapping.#ctor(System.String,System.Type,System.Boolean)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Storage.Internal.MySQLDateTypeMapping.#ctor(Microsoft.EntityFrameworkCore.Storage.RelationalTypeMapping.RelationalTypeMappingParameters)">
            <summary>
                This API supports the Entity Framework Core infrastructure and is not intended to be used
                directly from your code. This API may change or be removed in future releases.
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Storage.Internal.MySQLDateTypeMapping.Clone(Microsoft.EntityFrameworkCore.Storage.RelationalTypeMapping.RelationalTypeMappingParameters)">
            <summary>
                Creates a copy of this mapping.
            </summary>
            <param name="parameters"> The parameters for this mapping. </param>
            <returns> The newly created mapping. </returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Storage.Internal.MySQLDateTypeMapping.Clone(System.Boolean)">
            <summary>
                Creates a copy of this mapping.
            </summary>
            <param name="isDefaultValueCompatible"> Use a default value compatible syntax, or not. </param>
            <returns> The newly created mapping. </returns>
        </member>
        <member name="P:MySql.EntityFrameworkCore.Storage.Internal.MySQLDateTypeMapping.SqlLiteralFormatString">
            <summary>
                Gets the string format to be used to generate SQL literals of this type.
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Storage.Internal.MySQLDecimalTypeMapping.#ctor(Microsoft.EntityFrameworkCore.Storage.RelationalTypeMapping.RelationalTypeMappingParameters)">
            <summary>
              This API supports the Entity Framework Core infrastructure and is not intended to be used
              directly from your code. This API may change or be removed in future releases.
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Storage.Internal.MySQLDecimalTypeMapping.ConfigureParameter(System.Data.Common.DbParameter)">
            <summary>
              This is an internal API that supports the Entity Framework Core infrastructure and not subject to
              the same compatibility standards as public APIs. It may be changed or removed without notice in
              any release. You should only use it directly in your code with extreme caution and knowing that
              doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Storage.Internal.MySQLDoubleTypeMapping.#ctor(System.String,System.Nullable{System.Data.DbType})">
            <summary>
              This is an internal API that supports the Entity Framework Core infrastructure and not subject to
              the same compatibility standards as public APIs. It may be changed or removed without notice in
              any release. You should only use it directly in your code with extreme caution and knowing that
              doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Storage.Internal.MySQLDoubleTypeMapping.#ctor(Microsoft.EntityFrameworkCore.Storage.RelationalTypeMapping.RelationalTypeMappingParameters)">
            <summary>
              This API supports the Entity Framework Core infrastructure and is not intended to be used
              directly from your code. This API may change or be removed in future releases.
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Storage.Internal.MySQLDoubleTypeMapping.Clone(Microsoft.EntityFrameworkCore.Storage.RelationalTypeMapping.RelationalTypeMappingParameters)">
            <summary>
              Creates a copy of this mapping.
            </summary>
            <param name="parameters"> The parameters for this mapping. </param>
            <returns> The newly created mapping. </returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Storage.Internal.MySQLDoubleTypeMapping.ConfigureParameter(System.Data.Common.DbParameter)">
            <summary>
              This is an internal API that supports the Entity Framework Core infrastructure and not subject to
              the same compatibility standards as public APIs. It may be changed or removed without notice in
              any release. You should only use it directly in your code with extreme caution and knowing that
              doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="P:MySql.EntityFrameworkCore.Storage.Internal.MySQLExecutionStrategy.RetriesOnFailure">
            <inheritdoc/>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Storage.Internal.MySQLExecutionStrategy.Execute``2(``0,System.Func{Microsoft.EntityFrameworkCore.DbContext,``0,``1},System.Func{Microsoft.EntityFrameworkCore.DbContext,``0,Microsoft.EntityFrameworkCore.Storage.ExecutionResult{``1}})">
            <inheritdoc/>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Storage.Internal.MySQLExecutionStrategy.ExecuteAsync``2(``0,System.Func{Microsoft.EntityFrameworkCore.DbContext,``0,System.Threading.CancellationToken,System.Threading.Tasks.Task{``1}},System.Func{Microsoft.EntityFrameworkCore.DbContext,``0,System.Threading.CancellationToken,System.Threading.Tasks.Task{Microsoft.EntityFrameworkCore.Storage.ExecutionResult{``1}}},System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Storage.Internal.MySQLExecutionStrategyFactory.CreateDefaultStrategy(Microsoft.EntityFrameworkCore.Storage.ExecutionStrategyDependencies)">
            <inheritdoc/>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Storage.Internal.MySQLFloatTypeMapping.#ctor(System.String,System.Nullable{System.Data.DbType})">
            <summary>
              This is an internal API that supports the Entity Framework Core infrastructure and not subject to
              the same compatibility standards as public APIs. It may be changed or removed without notice in
              any release. You should only use it directly in your code with extreme caution and knowing that
              doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Storage.Internal.MySQLFloatTypeMapping.#ctor(Microsoft.EntityFrameworkCore.Storage.RelationalTypeMapping.RelationalTypeMappingParameters)">
            <summary>
              This is an internal API that supports the Entity Framework Core infrastructure and not subject to
              the same compatibility standards as public APIs. It may be changed or removed without notice in
              any release. You should only use it directly in your code with extreme caution and knowing that
              doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Storage.Internal.MySQLFloatTypeMapping.Clone(Microsoft.EntityFrameworkCore.Storage.RelationalTypeMapping.RelationalTypeMappingParameters)">
            <summary>
              Creates a copy of this mapping.
            </summary>
            <param name="parameters">The parameters for this mapping.</param>
            <returns>The newly created mapping.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Storage.Internal.MySQLFloatTypeMapping.GenerateNonNullSqlLiteral(System.Object)">
            <summary>
              This is an internal API that supports the Entity Framework Core infrastructure and not subject to
              the same compatibility standards as public APIs. It may be changed or removed without notice in
              any release. You should only use it directly in your code with extreme caution and knowing that
              doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Storage.Internal.MySQLFloatTypeMapping.ConfigureParameter(System.Data.Common.DbParameter)">
            <summary>
              This is an internal API that supports the Entity Framework Core infrastructure and not subject to
              the same compatibility standards as public APIs. It may be changed or removed without notice in
              any release. You should only use it directly in your code with extreme caution and knowing that
              doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="F:MySql.EntityFrameworkCore.Storage.Internal.MySQLJsonChangeTrackingOptions.None">
            <summary>
            The default is to serialize everything, which is the most precise, but also the slowest.
            </summary>
        </member>
        <member name="F:MySql.EntityFrameworkCore.Storage.Internal.MySQLJsonChangeTrackingOptions.CompareRootPropertyOnly">
            <summary>
            Do not track changes inside of JSON mapped properties but only for the root property itself.
            For example, if the JSON mapped property is a top level array of `int`, then changes to items of the
            array are not tracked, but changes to the array property itself (the reference) are.
            </summary>
        </member>
        <member name="F:MySql.EntityFrameworkCore.Storage.Internal.MySQLJsonChangeTrackingOptions.CompareStringRootPropertyByEquals">
            <summary>
            Compare strings as is, without further processing. This means that adding whitespaces between inner
            properties of a JSON object, that have no effect at all to the JSON object itself, would lead to a change
            being discovered to the JSON object, resulting in the JSON mapped property being marked as modified.
            </summary>
        </member>
        <member name="F:MySql.EntityFrameworkCore.Storage.Internal.MySQLJsonChangeTrackingOptions.CompareDomRootPropertyByEquals">
            <summary>
            Only check the JSON root property for DOM objects.
            </summary>
        </member>
        <member name="F:MySql.EntityFrameworkCore.Storage.Internal.MySQLJsonChangeTrackingOptions.CompareDomSemantically">
            <summary>
            Traverse the DOM to check for changes.
            </summary>
        </member>
        <member name="F:MySql.EntityFrameworkCore.Storage.Internal.MySQLJsonChangeTrackingOptions.HashDomSemantially">
            <summary>
            Fully traverse the DOM to generate a hash.
            </summary>
        </member>
        <member name="F:MySql.EntityFrameworkCore.Storage.Internal.MySQLJsonChangeTrackingOptions.HashDomSemantiallyOptimized">
            <summary>
            Traverse part of the DOM to generate a hash.
            </summary>
        </member>
        <member name="F:MySql.EntityFrameworkCore.Storage.Internal.MySQLJsonChangeTrackingOptions.SnapshotCallsDeepClone">
            <summary>
            Call DeepClone() whenever a type, for which a snapshot needs to be generated, implements it.
            </summary>
        </member>
        <member name="F:MySql.EntityFrameworkCore.Storage.Internal.MySQLJsonChangeTrackingOptions.SnapshotCallsClone">
            <summary>
            Call Clone() whenever a type, for which a snapshot needs to be generated, implements it.
            </summary>
        </member>
        <member name="P:MySql.EntityFrameworkCore.Storage.Internal.MySQLRelationalConnection.SupportsAmbientTransactions">
            <summary>
            Indicates whether the store connection supports ambient transactions
            </summary>
        </member>
        <member name="T:MySql.EntityFrameworkCore.Storage.Internal.MySQLSqlGenerationHelper">
            <summary>
            Provides the service to assist in generating SQL commands.
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Storage.Internal.MySQLSqlGenerationHelper.EscapeIdentifier(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Storage.Internal.MySQLSqlGenerationHelper.EscapeIdentifier(System.Text.StringBuilder,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Storage.Internal.MySQLSqlGenerationHelper.DelimitIdentifier(System.Text.StringBuilder,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Storage.Internal.MySQLSqlGenerationHelper.DelimitIdentifier(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Storage.Internal.MySQLSqlGenerationHelper.DelimitIdentifier(System.String,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Storage.Internal.MySQLStringTypeMapping.Clone(Microsoft.EntityFrameworkCore.Storage.RelationalTypeMapping.RelationalTypeMappingParameters)">
            <summary>
              Creates a copy of this mapping.
            </summary>
            <param name="parameters"> The parameters for this mapping. </param>
            <returns> The newly created mapping. </returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Storage.Internal.MySQLStringTypeMapping.ConfigureParameter(System.Data.Common.DbParameter)">
            <inheritdoc/>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Storage.Internal.MySQLTimeSpanMapping.Clone(Microsoft.EntityFrameworkCore.Storage.RelationalTypeMapping.RelationalTypeMappingParameters)">
            <summary>
              Creates a copy of this mapping.
            </summary>
            <param name="parameters"> The parameters for this mapping. </param>
            <returns> The newly created mapping. </returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Storage.Internal.MySQLTimeSpanMapping.GenerateNonNullSqlLiteral(System.Object)">
            <summary>
              Generates the MySQL representation of a non-null literal value.
            </summary>
            <param name="value">The literal value.</param>
            <returns>
              The generated string.
            </returns>
        </member>
        <member name="T:MySql.EntityFrameworkCore.Storage.Internal.MySQLTransientExceptionDetector">
            <summary>
            Detects the exceptions caused by MySQL Server transient failures.
            </summary>
        </member>
        <member name="P:MySql.EntityFrameworkCore.Storage.Internal.MySQLTypeMapping.MySqlDbType">
            <summary>
            The database type used by MySQL.
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Storage.Internal.MySQLTypeMappingSource.FindMapping(Microsoft.EntityFrameworkCore.Storage.RelationalTypeMappingInfo@)">
            <inheritdoc/>
        </member>
        <member name="T:MySql.EntityFrameworkCore.Update.IMySQLUpdateSqlGenerator">
            <summary>
              <para>
                The service lifetime is <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Singleton" />. This means a single instance
                is used by many <see cref="T:Microsoft.EntityFrameworkCore.DbContext" /> instances. The implementation must be thread-safe.
                This service cannot depend on services registered as <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Scoped" />.
              </para>
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Update.IMySQLUpdateSqlGenerator.AppendBulkInsertOperation(System.Text.StringBuilder,System.Collections.Generic.IReadOnlyList{Microsoft.EntityFrameworkCore.Update.IReadOnlyModificationCommand},System.Int32)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:MySql.EntityFrameworkCore.Update.MySQLModificationCommandBatch">
            <summary>
              AffectedCountModificationCommandBatch implementation for MySQL
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Update.MySQLModificationCommandBatch.#ctor(Microsoft.EntityFrameworkCore.Update.ModificationCommandBatchFactoryDependencies,System.Int32)">
            <summary>
              This is an internal API that supports the Entity Framework Core infrastructure and not subject to
              the same compatibility standards as public APIs. It may be changed or removed without notice in
              any release. You should only use it directly in your code with extreme caution and knowing that
              doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="P:MySql.EntityFrameworkCore.Update.MySQLModificationCommandBatch.MaxBatchSize">
            <summary>
              The maximum number of <see cref="T:Microsoft.EntityFrameworkCore.Update.ModificationCommand"/> instances that can be added to a single batch.
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Update.MySQLModificationCommandBatch.Complete(System.Boolean)">
            <summary>
              This is an internal API that supports the Entity Framework Core infrastructure and not subject to
              the same compatibility standards as public APIs. It may be changed or removed without notice in
              any release. You should only use it directly in your code with extreme caution and knowing that
              doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Update.MySQLModificationCommandBatch.Execute(Microsoft.EntityFrameworkCore.Storage.IRelationalConnection)">
            <summary>
              This is an internal API that supports the Entity Framework Core infrastructure and not subject to
              the same compatibility standards as public APIs. It may be changed or removed without notice in
              any release. You should only use it directly in your code with extreme caution and knowing that
              doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.Update.MySQLModificationCommandBatch.ExecuteAsync(Microsoft.EntityFrameworkCore.Storage.IRelationalConnection,System.Threading.CancellationToken)">
            <summary>
              This is an internal API that supports the Entity Framework Core infrastructure and not subject to
              the same compatibility standards as public APIs. It may be changed or removed without notice in
              any release. You should only use it directly in your code with extreme caution and knowing that
              doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:MySql.EntityFrameworkCore.Update.MySQLModificationCommandBatchFactory">
            <summary>
              IModificationCommandBatchFactory implemntation for MySQL
            </summary>
        </member>
        <member name="P:MySql.EntityFrameworkCore.Update.MySQLModificationCommandBatchFactory.Dependencies">
            <summary>
              Relational provider-specific dependencies for this service.
            </summary>
        </member>
        <member name="T:MySql.EntityFrameworkCore.MySQLUpdateSqlGenerator">
            <summary>
              <para>
                The service lifetime is <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Singleton" />. This means a single instance
                is used by many <see cref="T:Microsoft.EntityFrameworkCore.DbContext" /> instances. The implementation must be thread-safe.
                This service cannot depend on services registered as <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Scoped" />.
              </para>
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.MySQLUpdateSqlGenerator.AppendSelectAffectedCommand(System.Text.StringBuilder,System.String,System.String,System.Collections.Generic.IReadOnlyList{Microsoft.EntityFrameworkCore.Update.IColumnModification},System.Collections.Generic.IReadOnlyList{Microsoft.EntityFrameworkCore.Update.IColumnModification})">
            <summary>
            Appends a SQL command for selecting affected data.
            </summary>
            <param name="commandStringBuilder">The builder to which the SQL should be appended.</param>
            <param name="name">The name of the table.</param>
            <param name="schema">The table schema, or <see langword="null" /> to use the default schema.</param>
            <param name="readOperations">The operations representing the data to be read.</param>
            <param name="conditionOperations">The operations used to generate the <c>WHERE</c> clause for the select.</param>
            <returns>The <see cref="T:Microsoft.EntityFrameworkCore.Update.ResultSetMapping" /> for this command.</returns>
        </member>
        <member name="M:MySql.EntityFrameworkCore.MySQLUpdateSqlGenerator.AppendSelectCommandHeader(System.Text.StringBuilder,System.Collections.Generic.IReadOnlyList{Microsoft.EntityFrameworkCore.Update.IColumnModification})">
            <summary>
              Appends a SQL fragment for starting a <c>SELECT</c>.
            </summary>
            <param name="commandStringBuilder">The builder to which the SQL should be appended.</param>
            <param name="operations">The operations representing the data to be read.</param>
        </member>
        <member name="M:MySql.EntityFrameworkCore.MySQLUpdateSqlGenerator.AppendFromClause(System.Text.StringBuilder,System.String,System.String)">
            <summary>
              Appends a SQL fragment for starting a <c>FROM</c> clause.
            </summary>
            <param name="commandStringBuilder">The builder to which the SQL should be appended.</param>
            <param name="name">The name of the table.</param>
            <param name="schema">The table schema, or <see langword="null" /> to use the default schema.</param>
        </member>
        <member name="M:MySql.EntityFrameworkCore.MySQLUpdateSqlGenerator.AppendWhereAffectedClause(System.Text.StringBuilder,System.Collections.Generic.IReadOnlyList{Microsoft.EntityFrameworkCore.Update.IColumnModification})">
            <summary>
              Appends a <c>WHERE</c> clause involving rows affected.
            </summary>
            <param name="commandStringBuilder">The builder to which the SQL should be appended.</param>
            <param name="operations">The operations from which to build the conditions.</param>
        </member>
        <member name="M:MySql.EntityFrameworkCore.MySQLUpdateSqlGenerator.IsIdentityOperation(Microsoft.EntityFrameworkCore.Update.IColumnModification)">
            <summary>
              Returns a value indicating whether the given modification represents an auto-incrementing column.
            </summary>
            <param name="modification">The column modification.</param>
            <returns><see langword="true" /> if the given modification represents an auto-incrementing column.</returns>
        </member>
        <member name="T:MySql.EntityFrameworkCore.Utils.Statics">
            <summary>
            Initializes and retrieves an array of Boolean values with an specific number of elements and values.
            </summary>
        </member>
        <member name="T:MySql.EntityFrameworkCore.ValueGeneration.Internal.MySQLValueGeneratorSelector">
            <summary>
              <para>
                The service lifetime is <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Scoped" />. This means that each
                <see cref="T:Microsoft.EntityFrameworkCore.DbContext" /> instance will use its own instance of this service.
                The implementation may depend on other services registered with any lifetime.
                The implementation does not need to be thread-safe.
              </para>
            </summary>
        </member>
        <member name="M:MySql.EntityFrameworkCore.ValueGeneration.Internal.MySQLValueGeneratorSelector.Create(Microsoft.EntityFrameworkCore.Metadata.IProperty,Microsoft.EntityFrameworkCore.Metadata.IEntityType)">
            <inheritdoc/>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.MySQLDbContextOptionsExtensions">
            <summary>
            MySQL specific extension methods for <see cref="T:Microsoft.EntityFrameworkCore.DbContextOptionsBuilder" />.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.MySQLDbContextOptionsExtensions.UseMySQL(Microsoft.EntityFrameworkCore.DbContextOptionsBuilder,System.Action{MySql.EntityFrameworkCore.Infrastructure.MySQLDbContextOptionsBuilder})">
            <summary>
              <para>
                Configures the context to connect to a MySQL database, but without initially setting any
                <see cref="T:System.Data.Common.DbConnection" /> or connection string.
              </para>
              <para>
                The connection or connection string must be set before the <see cref="T:Microsoft.EntityFrameworkCore.DbContext" /> is used to connect
                to a database. Set a connection using <see cref="M:Microsoft.EntityFrameworkCore.RelationalDatabaseFacadeExtensions.SetDbConnection(Microsoft.EntityFrameworkCore.Infrastructure.DatabaseFacade,System.Data.Common.DbConnection,System.Boolean)" />.
                Set a connection string using <see cref="M:Microsoft.EntityFrameworkCore.RelationalDatabaseFacadeExtensions.SetConnectionString(Microsoft.EntityFrameworkCore.Infrastructure.DatabaseFacade,System.String)" />.
              </para>
            </summary>
            <param name="optionsBuilder">The builder being used to configure the context.</param>
            <param name="mySqlOptionsAction">An optional action to enable additional MySQL server-specific configuration.</param>
            <returns>The options builder so that further configuration can be chained.</returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.MySQLDbContextOptionsExtensions.UseMySQL(Microsoft.EntityFrameworkCore.DbContextOptionsBuilder,System.String,System.Action{MySql.EntityFrameworkCore.Infrastructure.MySQLDbContextOptionsBuilder})">
            <summary>
              Configures the context used to connect to a MySQL database.
            </summary>
            <param name="optionsBuilder">The builder being used to configure the context.</param>
            <param name="connectionString">The connection string of the database to connect to.</param>
            <param name="mySqlOptionsAction">An optional action to enable additional MySQL server-specific configuration.</param>
            <returns>The options builder so that further configuration can be chained.</returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.MySQLDbContextOptionsExtensions.UseMySQL(Microsoft.EntityFrameworkCore.DbContextOptionsBuilder,System.Data.Common.DbConnection,System.Action{MySql.EntityFrameworkCore.Infrastructure.MySQLDbContextOptionsBuilder})">
            <summary>
              Configures the context to connect to a MySQL database.
            </summary>
            <param name="optionsBuilder">The builder being used to configure the context.</param>
            <param name="connection">
              An existing <see cref="T:System.Data.Common.DbConnection" /> to be used to connect to the database. If the connection is
              in the open state, then Entity Framework (EF) will not open or close the connection. If the connection is in the closed
              state, then EF opens and closes the connection as needed.
            </param>
            <param name="mySqlOptionsAction">An optional action to enable additional MySQL server-specific configuration.</param>
            <returns>The options builder so that further configuration can be chained.</returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.MySQLDbContextOptionsExtensions.UseMySQL``1(Microsoft.EntityFrameworkCore.DbContextOptionsBuilder{``0},System.Action{MySql.EntityFrameworkCore.Infrastructure.MySQLDbContextOptionsBuilder})">
            <summary>
              <para>
                Configures the context to connect to a MySQL database, but without initially setting any
                <see cref="T:System.Data.Common.DbConnection" /> or connection string.
              </para>
              <para>
                The connection or connection string must be set before the <see cref="T:Microsoft.EntityFrameworkCore.DbContext" /> is used to connect
                to a database. Set a connection using <see cref="M:Microsoft.EntityFrameworkCore.RelationalDatabaseFacadeExtensions.SetDbConnection(Microsoft.EntityFrameworkCore.Infrastructure.DatabaseFacade,System.Data.Common.DbConnection,System.Boolean)" />.
                Set a connection string using <see cref="M:Microsoft.EntityFrameworkCore.RelationalDatabaseFacadeExtensions.SetConnectionString(Microsoft.EntityFrameworkCore.Infrastructure.DatabaseFacade,System.String)" />.
              </para>
            </summary>
            <param name="optionsBuilder">The builder being used to configure the context.</param>
            <param name="mySqlOptionsAction">An optional action to enable additional MySQL server-specific configuration.</param>
            <returns>The options builder so that further configuration can be chained.</returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.MySQLDbContextOptionsExtensions.UseMySQL``1(Microsoft.EntityFrameworkCore.DbContextOptionsBuilder{``0},System.String,System.Action{MySql.EntityFrameworkCore.Infrastructure.MySQLDbContextOptionsBuilder})">
            <summary>
              Configures the context to connect to a MySQL database.
            </summary>
            <typeparam name="TContext">The type of context to be configured.</typeparam>
            <param name="optionsBuilder">The builder being used to configure the context.</param>
            <param name="connectionString">The connection string of the database to connect to.</param>
            <param name="mySqlOptionsAction">An optional action to enable additional MySQL server-specific configuration.</param>
            <returns>The options builder so that further configuration can be chained.</returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.MySQLDbContextOptionsExtensions.UseMySQL``1(Microsoft.EntityFrameworkCore.DbContextOptionsBuilder{``0},System.Data.Common.DbConnection,System.Action{MySql.EntityFrameworkCore.Infrastructure.MySQLDbContextOptionsBuilder})">
            <summary>
              Configures the context to connect to a MySQL database.
            </summary>
            <typeparam name="TContext">The type of context to be configured.</typeparam>
            <param name="optionsBuilder">The builder being used to configure the context.</param>
            <param name="connection">
              An existing <see cref="T:System.Data.Common.DbConnection" /> to be used to connect to the database. If the connection is
              in the open state, then Entity Framework (EF) will not open or close the connection. If the connection is in the closed
              state, then EF will open and close the connection as needed.
            </param>
            <param name="mySqlOptionsAction">An optional action to enable additional MySQL server-specific configuration.</param>
            <returns>The options builder so that further configuration can be chained.</returns>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Query.Internal.MySQLEvaluatableExpressionFilter">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Query.Internal.MySQLEvaluatableExpressionFilter.#ctor(Microsoft.EntityFrameworkCore.Query.EvaluatableExpressionFilterDependencies,Microsoft.EntityFrameworkCore.Query.RelationalEvaluatableExpressionFilterDependencies)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Query.Internal.MySQLEvaluatableExpressionFilter.IsEvaluatableExpression(System.Linq.Expressions.Expression,Microsoft.EntityFrameworkCore.Metadata.IModel)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
    </members>
</doc>
