from flask import Flask, render_template, send_from_directory, request, jsonify, redirect, url_for, session
import csv
from dataclasses import dataclass
from typing import List
import os
import subprocess
from datetime import datetime, timedelta
from functools import wraps
import pandas as pd
import requests


app = Flask(__name__)
app.secret_key = 'uproar321'

# Discord API configuration
DISCORD_BOT_TOKEN = os.getenv('DISCORD_BOT_TOKEN')
DISCORD_GUILD_ID = os.getenv('DISCORD_GUILD_ID')

DISCORD_RAIDER_ROLE_ID = '1173303263832064050'

# Add at the top with other environment variables
SUDO_PASSWORD = os.getenv('SUDO_PASSWORD')

def assign_discord_role(user_id: str) -> bool:
    """Assign the Uproar Raider role to a Discord user."""
    if not DISCORD_BOT_TOKEN or not DISCORD_GUILD_ID:
        print("Discord API configuration missing")
        return False
        
    url = f"https://discord.com/api/v10/guilds/{DISCORD_GUILD_ID}/members/{user_id}/roles/{DISCORD_RAIDER_ROLE_ID}"
    headers = {
        "Authorization": f"Bot {DISCORD_BOT_TOKEN}",
        "Content-Type": "application/json"
    }
    
    try:
        # First, get the bot's own information to verify it's working
        bot_info_url = "https://discord.com/api/v10/users/@me"
        bot_response = requests.get(bot_info_url, headers=headers)
        bot_response.raise_for_status()
        print(f"Bot is authenticated as: {bot_response.json().get('username')}")
        
        # Get guild member information to verify the user exists
        member_url = f"https://discord.com/api/v10/guilds/{DISCORD_GUILD_ID}/members/{user_id}"
        member_response = requests.get(member_url, headers=headers)
        member_response.raise_for_status()
        print(f"Found user in guild: {member_response.json().get('user', {}).get('username')}")
        
        # Get guild roles to verify the role exists and bot has proper permissions
        roles_url = f"https://discord.com/api/v10/guilds/{DISCORD_GUILD_ID}/roles"
        roles_response = requests.get(roles_url, headers=headers)
        roles_response.raise_for_status()
        
        # Find the bot's role and the target role
        bot_roles = member_response.json().get('roles', [])
        bot_role_positions = [role['position'] for role in roles_response.json() if role['id'] in bot_roles]
        target_role = next((role for role in roles_response.json() if role['id'] == DISCORD_RAIDER_ROLE_ID), None)
        
        if not target_role:
            print(f"Target role {DISCORD_RAIDER_ROLE_ID} not found in guild")
            return False
            
        if not bot_role_positions:
            print("Bot has no roles in the guild")
            return False
            
        if max(bot_role_positions) <= target_role['position']:
            print(f"Bot's highest role position ({max(bot_role_positions)}) is not higher than target role position ({target_role['position']})")
            return False
        
        # If all checks pass, assign the role
        response = requests.put(url, headers=headers)
        response.raise_for_status()
        print(f"Successfully assigned role to user {user_id}")
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"Error assigning Discord role: {e}")
        if hasattr(e.response, 'text'):
            print(f"Discord API response: {e.response.text}")
        return False

def remove_discord_role(user_id: str) -> bool:
    """Remove the Uproar Raider role from a Discord user."""
    if not DISCORD_BOT_TOKEN or not DISCORD_GUILD_ID:
        print("Discord API configuration missing")
        return False
        
    url = f"https://discord.com/api/v10/guilds/{DISCORD_GUILD_ID}/members/{user_id}/roles/{DISCORD_RAIDER_ROLE_ID}"
    headers = {
        "Authorization": f"Bot {DISCORD_BOT_TOKEN}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.delete(url, headers=headers)
        response.raise_for_status()
        print(f"Successfully removed role from user {user_id}")
        return True
    except requests.exceptions.RequestException as e:
        print(f"Error removing Discord role: {e}")
        if hasattr(e.response, 'text'):
            print(f"Discord API response: {e.response.text}")
        return False

def requires_password(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not session.get('authenticated'):
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

@dataclass
class Character:
    VALID_CLASSES = {
        "Death Knight", "Demon Hunter", "Druid", "Evoker", 
        "Hunter", "Mage", "Monk", "Paladin", 
        "Priest", "Rogue", "Shaman", "Warlock", "Warrior"
    }
    
    id: int
    name: str
    realm: str
    role: str
    class_name: str
    spec: str
    armor_type: str
    tier_token: str

    def __post_init__(self):
        if self.class_name not in self.VALID_CLASSES:
            raise ValueError(f"Invalid class name: {self.class_name}")

    @property
    def role_icon(self) -> str:
        role_icons = {
            "Tank": "https://cdn.raiderio.net/assets/img/role_tank-6cee7610058306ba277e82c392987134.png",
            "DPS": "https://cdn.raiderio.net/assets/img/role_dps-eb25989187d4d3ac866d609dc009f090.png",
            "Ranged DPS": "https://cdn.raiderio.net/assets/img/role_dps-eb25989187d4d3ac866d609dc009f090.png",
            "Melee DPS": "https://cdn.raiderio.net/assets/img/role_dps-eb25989187d4d3ac866d609dc009f090.png",
            "Healer": "https://cdn.raiderio.net/assets/img/role_healer-984e5e9867d6508a714a9c878d87441b.png"
        }
        return role_icons.get(self.role, "")

def load_characters() -> List[Character]:
    characters = []
    try:
        with open('PS_version/characters.csv', 'r', newline='') as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                character = Character(
                    id=int(row['id']),
                    name=row['name'],
                    realm=row['realm'],
                    role=row['role'],
                    class_name=row['class_name'],
                    spec=row['spec'],
                    armor_type=row['armor_type'],
                    tier_token=row['tier_token']
                )
                characters.append(character)
    except FileNotFoundError:
        # If CSV doesn't exist, create it with sample data
        sample_characters = [
            Character(
                id=1,
                name="Seeddy",
                realm="Stormscale",
                role="DPS",
                class_name="Mage",
                spec="Arcane",
                armor_type="Cloth",
                tier_token="Vestments"
            ),
            Character(
                id=2,
                name="Frosty",
                realm="Silvermoon",
                role="DPS",
                class_name="Mage",
                spec="Frost",
                armor_type="Cloth",
                tier_token="Vestments"
            ),
            Character(
                id=3,
                name="Healbot",
                realm="Draenor",
                role="Healer",
                class_name="Priest",
                spec="Holy",
                armor_type="Cloth",
                tier_token="Vestments"
            )
        ]
        # Save sample data to CSV
        with open('PS_version/characters.csv', 'w', newline='') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=vars(sample_characters[0]).keys())
            writer.writeheader()
            for character in sample_characters:
                writer.writerow(vars(character))
        characters = sample_characters
    return characters

def load_armory_urls() -> List[str]:
    urls = []
    csv_path = 'PS_version/csv_registers.csv'
    try:
        with open(csv_path, 'r', newline='') as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                if 'url' in row and row['url'].strip():
                    urls.append(row['url'].strip())
    except FileNotFoundError:
        # Create the directory if it doesn't exist
        os.makedirs(os.path.dirname(csv_path), exist_ok=True)
        # Create empty CSV file with header
        with open(csv_path, 'w', newline='') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(['url'])
    return urls

def save_armory_urls(urls: List[str]):
    csv_path = 'PS_version/csv_registers.csv'
    with open(csv_path, 'w', newline='') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(['url'])
        for url in urls:
            writer.writerow([url])

def get_last_updated(file_paths=None):
    """
    Get the last updated timestamp for the specified files.
    If no files are specified, returns current time.

    Args:
        file_paths: List of file paths to check, or single file path as string

    Returns:
        Formatted timestamp string of the most recent modification
    """
    try:
        if file_paths is None:
            return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # Convert single file path to list
        if isinstance(file_paths, str):
            file_paths = [file_paths]

        latest_time = None

        for file_path in file_paths:
            if os.path.exists(file_path):
                file_mtime = os.path.getmtime(file_path)
                file_datetime = datetime.fromtimestamp(file_mtime)

                if latest_time is None or file_datetime > latest_time:
                    latest_time = file_datetime

        if latest_time is None:
            return "No files found"

        return latest_time.strftime("%Y-%m-%d %H:%M:%S")
    except Exception as e:
        print(f"Error getting last updated time: {e}")
        return "Unknown"

# Add this function to determine the week number based on a date
def get_week_number(date):
    # EU reset is on Wednesday
    # Calculate days since the first Wednesday of the expansion
    # Assuming the expansion started on a specific date (adjust as needed)
    expansion_start = datetime(2025, 3, 5)  # Example start date (first Wednesday)
    days_since_start = (date - expansion_start).days
    return (days_since_start // 7) + 1

# Add this function to determine if a date is in the current week
def is_current_week(date_str):
    # Parse the date string (format: "May 31, 2025")
    date = datetime.strptime(date_str, "%b %d, %Y")
    
    # Get today's date
    today = datetime.now()
    
    # Calculate the most recent Wednesday (EU reset day)
    days_since_wednesday = (today.weekday() - 2) % 7
    last_reset = today - timedelta(days=days_since_wednesday)
    
    # Check if the date is after the last reset
    return date >= last_reset

@app.route('/')
def index():
    try:
        # Read the CSV file
        df = pd.read_csv('PS_version/characters.csv')

        # Get armory URLs from csv_registers.csv
        url_map = {}
        try:
            registers_df = pd.read_csv('PS_version/csv_registers.csv')
            for _, row in registers_df.iterrows():
                url = row['url']
                if url and 'character' in url:
                    # Extract character name from URL
                    if url.strip() and url.strip().split('/')[-1]:
                        char_name = url.strip().split('/')[-1].capitalize()
                        url_map[char_name] = url.strip()
        except Exception as e:
            print(f"Error reading csv_registers.csv: {e}")

        # Get tier pieces data from csv_tier_pieces.csv
        tier_map = {}
        try:
            tier_df = pd.read_csv('PS_version/csv_tier_pieces.csv')
            for _, row in tier_df.iterrows():
                tier_map[row['name']] = int(row['tier_Pieces'])
        except Exception as e:
            print(f"Error reading csv_tier_pieces.csv: {e}")

        # Convert DataFrame to list of dictionaries
        characters = []
        for _, row in df.iterrows():
            character = {
                'name': row['name'],
                'ilvl': row['ilvl'],
                'role': row['role'],
                'class_name': row['class_name'],
                'spec': row['spec'],
                'armor_type': row['armor_type'],
                'tier_pieces': tier_map.get(row['name'], 0),  # Add tier pieces count
                'tier_token': row['tier_token'],
                'url': url_map.get(row['name'], '#')  # Add URL from map or default to #
            }
            characters.append(character)

        return render_template('index.html', characters=characters,
                              last_updated=get_last_updated(['PS_version/characters.csv', 'PS_version/csv_registers.csv', 'PS_version/csv_tier_pieces.csv']))
    except Exception as e:
        print(f"Error: {e}")
        return render_template('index.html', characters=[],
                              last_updated=get_last_updated(['PS_version/characters.csv', 'PS_version/csv_registers.csv', 'PS_version/csv_tier_pieces.csv']))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        if request.form.get('password') == 'uproar321':
            session['authenticated'] = True
            return redirect(url_for('raiders'))
        return render_template('login.html', error='Invalid password')
    return render_template('login.html')

@app.route('/raiders')
@requires_password
def raiders():
    try:
        # Read the CSVs with ID as string
        df = pd.read_csv('PS_version/csv_registers.csv', dtype={'discord_id': str})
        discord_df = pd.read_csv('discord_exports/csv_user_export.csv', dtype={'ID': str})
        
        print("Loaded registers CSV:", df.shape)
        print("Loaded Discord export CSV:", discord_df.shape)
        
        # Create a dictionary mapping Discord IDs to their raider status
        discord_roles = {}
        for _, row in discord_df.iterrows():
            user_id = str(row['ID'])  # Convert ID to string for consistency
            is_raider = str(row['Raider']).lower() == 'true'
            discord_roles[user_id] = 'Assigned' if is_raider else 'Not assigned'
            if is_raider:
                print(f"Found Raider: {row['Username']} (ID: {user_id})")
        
        # Add role information to the armory_urls list
        armory_urls = []
        for _, row in df.iterrows():
            discord_id = str(row.get('discord_id', ''))  # Get Discord ID from registers
            role = discord_roles.get(discord_id, 'Not assigned')
            print(f"Checking discord ID: {discord_id}, role status: {role}")
            armory_urls.append((row['url'], row['discord'], row['status'], role))
        
        return render_template('raiders.html', armory_urls=armory_urls,
                              last_updated=get_last_updated(['PS_version/csv_registers.csv', 'discord_exports/csv_user_export.csv']))
    except Exception as e:
        print(f"Error in raiders route: {e}")
        return render_template('raiders.html', armory_urls=[],
                              last_updated=get_last_updated(['PS_version/csv_registers.csv', 'discord_exports/csv_user_export.csv']))

@app.route('/favicon.ico')
def favicon():
    return send_from_directory('static', 'favicon.ico')

@app.route('/add_url', methods=['POST'])
def add_url():
    data = request.get_json()
    if not data or not all(k in data for k in ['url', 'discord', 'discord_id']):
        return jsonify({'error': 'Missing required fields'}), 400

    try:
        # Read existing URLs
        df = pd.read_csv('PS_version/csv_registers.csv')
        
        # Check if URL already exists
        if data['url'] in df['url'].values:
            return jsonify({'error': 'URL already exists'}), 400
            
        # Check if Discord ID already exists
        if str(data['discord_id']) in df['discord_id'].values.astype(str):
            return jsonify({'error': 'Discord ID already registered'}), 400

        # Add new row
        new_row = {
            'url': data['url'],
            'discord': data['discord'],
            'status': 'pending',
            'discord_id': str(data['discord_id'])  # Ensure ID is stored as string
        }
        
        # Append new row to DataFrame
        df = pd.concat([df, pd.DataFrame([new_row])], ignore_index=True)
        
        # Save updated CSV
        df.to_csv('PS_version/csv_registers.csv', index=False)
        
        return jsonify({'message': 'URL added successfully'})
    except Exception as e:
        print(f"Error adding URL: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/remove_url', methods=['POST'])
def remove_url():
    data = request.get_json()
    if not data or 'url' not in data:
        return jsonify({'error': 'No URL provided'}), 400
    
    try:
        # Read the CSVs with ID as string
        df = pd.read_csv('PS_version/csv_registers.csv', dtype={'discord_id': str})
        discord_df = pd.read_csv('discord_exports/csv_user_export.csv', dtype={'ID': str})
        
        # Find the row with the matching URL
        mask = df['url'] == data['url'].strip()
        if not mask.any():
            return jsonify({'error': 'URL not found'}), 404
            
        # Get the Discord ID
        discord_id = str(df.loc[mask, 'discord_id'].iloc[0])
        print(f"Looking for Discord ID: {discord_id}")
        
        # Find the user in Discord export by ID
        discord_user = discord_df[discord_df['ID'].astype(str) == discord_id]
        
        if not discord_user.empty:
            roles = discord_user['Roles'].iloc[0]
            print(f"Found user, current roles: {roles}")
            
            # Check if user has the role
            if pd.notna(roles) and 'Uproar Raider' in roles:
                # Remove the role using Discord API
                if remove_discord_role(discord_id):
                    print(f"Successfully removed role via Discord API")
                    # Update the CSV to reflect the change
                    role_index = discord_df[discord_df['ID'].astype(str) == discord_id].index[0]
                    
                    # Update roles
                    current_roles = discord_df.at[role_index, 'Roles']
                    if current_roles == 'Uproar Raider':
                        discord_df.at[role_index, 'Roles'] = ''
                    else:
                        discord_df.at[role_index, 'Roles'] = current_roles.replace(', Uproar Raider', '').replace('Uproar Raider, ', '')
                    
                    # Update Raider status
                    discord_df.at[role_index, 'Raider'] = 'False'
                    
                    # Save changes to CSV
                    discord_df.to_csv('discord_exports/csv_user_export.csv', index=False)
                    print(f"Updated CSV file with new role information")
                else:
                    print("Failed to remove role via Discord API")
                    return jsonify({'error': 'Failed to remove Discord role'}), 500
            else:
                print(f"User doesn't have the Raider role")
        else:
            print(f"Could not find Discord user with ID: {discord_id}")
            return jsonify({'error': f'Discord user not found: {discord_id}'}), 404
        
        # Update the status to inactive
        df.loc[mask, 'status'] = 'inactive'
        df.to_csv('PS_version/csv_registers.csv', index=False)
        print(f"Updated registers CSV status to inactive")
        
        return jsonify({'message': 'Raider status updated to inactive'}), 200
    except Exception as e:
        print(f"Error in remove_url: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/update_data', methods=['POST'])
def update_data():
    try:
        if not SUDO_PASSWORD:
            print("Error: SUDO_PASSWORD environment variable not set")
            return jsonify({'error': 'Sudo password not configured'}), 500

        # Create the command (without sudo prefix since we're adding it to subprocess)
        command = "docker exec powershell-container pwsh -file PS_version/Main.ps1"
        command_args = command.split()

        print(f"Executing command: sudo {command}")

        # Execute the command with sudo password
        process = subprocess.Popen(['sudo', '-S'] + command_args,
                                 stdin=subprocess.PIPE,
                                 stdout=subprocess.PIPE,
                                 stderr=subprocess.PIPE,
                                 universal_newlines=True)

        # Send the password to stdin
        stdout, stderr = process.communicate(input=f"{SUDO_PASSWORD}\n")

        print(f"Command return code: {process.returncode}")
        print(f"Command stdout: {stdout}")
        print(f"Command stderr: {stderr}")

        if process.returncode != 0:
            print(f"Error executing command: {stderr}")
            return jsonify({'error': f'Failed to update data: {stderr}'}), 500

        # Get current timestamp
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        return jsonify({
            'message': 'Data update completed successfully',
            'timestamp': timestamp,
            'output': stdout
        })

    except Exception as e:
        print(f"Error in update_data: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/approve_url', methods=['POST'])
def approve_url():
    data = request.get_json()
    url = data.get('url')
    
    if not url:
        return jsonify({'error': 'No URL provided'}), 400
        
    try:
        # Read the CSVs with ID as string
        df = pd.read_csv('PS_version/csv_registers.csv', dtype={'discord_id': str})
        discord_df = pd.read_csv('discord_exports/csv_user_export.csv', dtype={'ID': str})
        
        # Find the row with the matching URL
        mask = df['url'] == url
        if not mask.any():
            return jsonify({'error': 'URL not found'}), 404
            
        # Get the Discord ID
        discord_id = str(df.loc[mask, 'discord_id'].iloc[0])
        print(f"Looking for Discord ID: {discord_id}")
        
        # Find the user in Discord export by ID
        discord_user = discord_df[discord_df['ID'].astype(str) == discord_id]
        
        if not discord_user.empty:
            roles = discord_user['Roles'].iloc[0]
            print(f"Found user, current roles: {roles}")
            
            # Check if user already has the role
            if pd.isna(roles) or 'Uproar Raider' not in roles:
                print(f"Attempting to assign role to user {discord_user['Username'].iloc[0]}")
                # Assign the role using Discord API
                if assign_discord_role(discord_id):
                    print(f"Successfully assigned role via Discord API")
                    # Update the CSV to reflect the change
                    role_index = discord_df[discord_df['ID'].astype(str) == discord_id].index[0]
                    
                    # Update roles
                    current_roles = discord_df.at[role_index, 'Roles']
                    if pd.isna(current_roles):
                        discord_df.at[role_index, 'Roles'] = 'Uproar Raider'
                    else:
                        discord_df.at[role_index, 'Roles'] = f"{current_roles}, Uproar Raider"
                    
                    # Update Raider status
                    discord_df.at[role_index, 'Raider'] = 'True'
                    
                    # Save changes to CSV
                    discord_df.to_csv('discord_exports/csv_user_export.csv', index=False)
                    print(f"Updated CSV file with new role information")
                else:
                    print("Failed to assign role via Discord API")
                    return jsonify({'error': 'Failed to assign Discord role'}), 500
            else:
                print(f"User already has the Raider role")
        else:
            print(f"Could not find Discord user with ID: {discord_id}")
            return jsonify({'error': f'Discord user not found: {discord_id}'}), 404
        
        # Update the status in registers CSV
        df.loc[mask, 'status'] = 'approved'
        df.to_csv('PS_version/csv_registers.csv', index=False)
        print(f"Updated registers CSV status to approved")
        
        return jsonify({'message': 'URL approved successfully'})
    except Exception as e:
        print(f"Error in approve_url: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/reactivate_url', methods=['POST'])
def reactivate_url():
    data = request.get_json()
    if not data or 'url' not in data:
        return jsonify({'error': 'No URL provided'}), 400
    
    try:
        # Read the CSVs with ID as string
        df = pd.read_csv('PS_version/csv_registers.csv', dtype={'discord_id': str})
        discord_df = pd.read_csv('discord_exports/csv_user_export.csv', dtype={'ID': str})
        
        # Find the row with the matching URL
        mask = df['url'] == data['url'].strip()
        if not mask.any():
            return jsonify({'error': 'URL not found'}), 404
            
        # Get the Discord ID
        discord_id = str(df.loc[mask, 'discord_id'].iloc[0])
        print(f"Looking for Discord ID: {discord_id}")
        
        # Find the user in Discord export by ID
        discord_user = discord_df[discord_df['ID'].astype(str) == discord_id]
        
        if not discord_user.empty:
            roles = discord_user['Roles'].iloc[0]
            print(f"Found user, current roles: {roles}")
            
            # Check if user already has the role
            if pd.isna(roles) or 'Uproar Raider' not in roles:
                print(f"Attempting to assign role to user {discord_user['Username'].iloc[0]}")
                # Assign the role using Discord API
                if assign_discord_role(discord_id):
                    print(f"Successfully assigned role via Discord API")
                    # Update the CSV to reflect the change
                    role_index = discord_df[discord_df['ID'].astype(str) == discord_id].index[0]
                    
                    # Update roles
                    current_roles = discord_df.at[role_index, 'Roles']
                    if pd.isna(current_roles):
                        discord_df.at[role_index, 'Roles'] = 'Uproar Raider'
                    else:
                        discord_df.at[role_index, 'Roles'] = f"{current_roles}, Uproar Raider"
                    
                    # Update Raider status
                    discord_df.at[role_index, 'Raider'] = 'True'
                    
                    # Save changes to CSV
                    discord_df.to_csv('discord_exports/csv_user_export.csv', index=False)
                    print(f"Updated CSV file with new role information")
                else:
                    print("Failed to assign role via Discord API")
                    return jsonify({'error': 'Failed to assign Discord role'}), 500
            else:
                print(f"User already has the Raider role")
        else:
            print(f"Could not find Discord user with ID: {discord_id}")
            return jsonify({'error': f'Discord user not found: {discord_id}'}), 404
        
        # Update the status to approved
        df.loc[mask, 'status'] = 'approved'
        df.to_csv('PS_version/csv_registers.csv', index=False)
        print(f"Updated registers CSV status to approved")
        
        return jsonify({'message': 'Raider reactivated successfully'}), 200
    except Exception as e:
        print(f"Error in reactivate_url: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/update_discord_ids', methods=['POST'])
def update_discord_ids():
    try:
        # Read the CSVs
        registers_df = pd.read_csv('PS_version/csv_registers.csv')
        discord_df = pd.read_csv('discord_exports/csv_user_export.csv', dtype={'ID': str})  # Force ID as string
        
        print("Initial registers data:", registers_df.head())
        print("Discord data:", discord_df.head())
        
        # Create a mapping of usernames to IDs
        discord_id_map = {}
        for _, row in discord_df.iterrows():
            full_username = row['Username']
            base_username = full_username.split(' (')[0].strip()
            base_username_no_dots = base_username.replace('.', '')
            # Ensure ID is stored as string
            discord_id_map[full_username] = str(row['ID'])
            discord_id_map[base_username] = str(row['ID'])
            discord_id_map[base_username_no_dots] = str(row['ID'])
            print(f"Mapped {full_username} -> {row['ID']}")
            
        # Add or update discord_id column
        registers_df['discord_id'] = registers_df['discord'].map(discord_id_map)
        
        print("Updated registers data:", registers_df.head())
        
        # Save updated registers CSV with ID as string
        registers_df.to_csv('PS_version/csv_registers.csv', index=False)
        
        return jsonify({'message': 'Discord IDs updated successfully'})
    except Exception as e:
        print(f"Error updating Discord IDs: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/tier')
def tier():
    try:
        # Get the selected date from query parameters, default to 'current'
        selected_date = request.args.get('date', 'current')
        
        # Determine which file to read
        if selected_date == 'current':
            file_path = 'PS_version/csv_tier_pieces.csv'
        else:
            file_path = f'PS_version/Archive/Tier/{selected_date}_csv_tier_pieces.csv'
        
        # Read the tier pieces CSV
        df = pd.read_csv(file_path)
        
        # Convert DataFrame to list of dictionaries
        characters = []
        for _, row in df.iterrows():
            character = {
                'name': row['name'],
                'realm': row['realm'],
                'token': row['token'],
                'head': int(row['Head']),
                'shoulders': int(row['Shoulders']),
                'chest': int(row['Chest']),
                'hands': int(row['Hands']),
                'legs': int(row['Legs'])
            }
            characters.append(character)
            
        return render_template('tier.html', characters=characters,
                              last_updated=get_last_updated(file_path), selected_date=selected_date)
    except Exception as e:
        print(f"Error: {e}")
        return render_template('tier.html', characters=[],
                              last_updated=get_last_updated('PS_version/csv_tier_pieces.csv'), selected_date='current')

@app.route('/get_historical_dates')
def get_historical_dates():
    try:
        # Get list of historical files
        archive_dir = 'PS_version/Archive/Tier'
        files = [f for f in os.listdir(archive_dir) if f.endswith('_csv_tier_pieces.csv')]
        
        # Extract dates from filenames and sort them
        dates = [f.split('_')[0] for f in files]
        dates.sort(reverse=True)  # Most recent first
        
        return jsonify({'dates': dates})
    except Exception as e:
        print(f"Error getting historical dates: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/enchants')
def enchants():
    try:
        # Get the selected date from query parameters, default to 'current'
        selected_date = request.args.get('date', 'current')
        
        # Determine which file to read
        if selected_date == 'current':
            file_path = 'PS_version/csv_enchants.csv'
        else:
            file_path = f'PS_version/Archive/Enchants/{selected_date}_csv_enchants.csv'
        
        # Read the enchants CSV
        df = pd.read_csv(file_path)
        
        # Convert DataFrame to list of dictionaries
        characters = []
        for _, row in df.iterrows():
            character = {
                'name': row['name'],
                'realm': row['realm'],
                'Head': row['Head'],
                'Back': row['Back'],
                'Chest': row['Chest'],
                'wrist': row['wrist'],
                'Legs': row['Legs'],
                'feet': row['feet'],
                'finger_1': row['finger_1'],
                'finger_2': row['finger_2'],
                'Main_hand': row['Main_hand']
            }
            characters.append(character)
            
        return render_template('enchants.html', characters=characters,
                              last_updated=get_last_updated(file_path), selected_date=selected_date)
    except Exception as e:
        print(f"Error: {e}")
        return render_template('enchants.html', characters=[],
                              last_updated=get_last_updated('PS_version/csv_enchants.csv'), selected_date='current')

@app.route('/get_historical_enchant_dates')
def get_historical_enchant_dates():
    try:
        # Get list of files in the archive directory
        archive_path = 'PS_version/Archive/Enchants/'
        files = [f for f in os.listdir(archive_path) if f.endswith('_csv_enchants.csv')]
        
        # Extract dates from filenames (format: YYYYMMDD_csv_enchants.csv)
        dates = [f.split('_')[0] for f in files]
        
        # Sort dates in descending order (newest first)
        dates.sort(reverse=True)
        
        return jsonify({'dates': dates})
    except Exception as e:
        print(f"Error getting historical enchant dates: {e}")
        return jsonify({'dates': []})

@app.route('/logs')
def logs():
    try:
        # Check if the CSV file exists
        csv_path = 'PS_version/csv_logs.csv'
        if not os.path.exists(csv_path):
            print(f"Warning: Log file {csv_path} does not exist")
            # Create a sample CSV file with headers
            sample_data = pd.DataFrame({
                'Date': ['2023-10-15', '2023-10-15', '2023-10-16'],
                'Encounter': ['Gnarlroot', 'Igira the Cruel', 'Council of Dreams'],
                'Duration': ['5:23', '7:45', '8:12'],
                'Kill': ['True', 'False', 'True'],
                'Owner': ['Seeddy', 'Frosty', 'Healbot'],
                'Link': ['https://www.warcraftlogs.com/reports/sample1', 
                         'https://www.warcraftlogs.com/reports/sample2',
                         'https://www.warcraftlogs.com/reports/sample3'],
                'Zone': ['Amirdrassil', 'Amirdrassil', 'Amirdrassil']
            })
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(csv_path), exist_ok=True)
            # Save sample data
            sample_data.to_csv(csv_path, index=False)
            print(f"Created sample log file at {csv_path}")
        
        # Read the CSV file
        df = pd.read_csv(csv_path)
        
        # Check if required columns exist
        required_columns = ['Date', 'Encounter', 'Duration', 'Kill', 'Owner', 'Link', 'Zone']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            print(f"Warning: Missing columns in CSV: {missing_columns}")
            # Add missing columns with default values
            for col in missing_columns:
                df[col] = 'Unknown'
        
        # Filter out encounters containing "+"
        if 'Encounter' in df.columns:
            df = df[~df['Encounter'].str.contains('\+', regex=True, na=False)]
        
        # Get unique zones for the dropdown
        zones = sorted(df['Zone'].unique()) if 'Zone' in df.columns else []
        
        # Get filter parameters from query
        selected_zone = request.args.get('zone', '')
        selected_kill = request.args.get('kill', '')
        show_duplicates = request.args.get('show_duplicates', '') == 'true'
        
        # If no kill filter is specified, default to showing only kills
        if not selected_kill and not request.args.get('show_all', ''):
            selected_kill = 'true'
        
        # Apply zone filter if selected
        if selected_zone and 'Zone' in df.columns:
            df = df[df['Zone'] == selected_zone]
        
        # Apply kill filter if selected
        if selected_kill and 'Kill' in df.columns:
            is_kill = selected_kill.lower() == 'true'
            # Convert Kill column to boolean for comparison
            df['Kill'] = df['Kill'].astype(str).str.lower() == 'true'
            df = df[df['Kill'] == is_kill]
        
        # Convert Date column to datetime for proper sorting
        if 'Date' in df.columns:
            df['Date'] = pd.to_datetime(df['Date'], errors='coerce')
        
        # Remove duplicate kill logs (same encounter, date, and duration) unless show_duplicates is true
        if not show_duplicates and 'Encounter' in df.columns and 'Date' in df.columns and 'Duration' in df.columns:
            # Keep only the first occurrence of each unique combination
            df = df.drop_duplicates(subset=['Encounter', 'Date', 'Duration'], keep='first')
        
        # Sort by date (most recent first)
        if 'Date' in df.columns:
            df = df.sort_values('Date', ascending=False)
        
        # Convert DataFrame to list of dictionaries
        logs = []
        for _, row in df.iterrows():
            log = {
                'date': row.get('Date', 'Unknown').strftime('%Y-%m-%d') if isinstance(row.get('Date'), pd.Timestamp) else row.get('Date', 'Unknown'),
                'encounter': row.get('Encounter', 'Unknown'),
                'duration': row.get('Duration', 'Unknown'),
                'kill': str(row.get('Kill', '')).lower() == 'true',
                'owner': row.get('Owner', 'Unknown'),
                'link': row.get('Link', '#'),
                'zone': row.get('Zone', 'Unknown')
            }
            logs.append(log)
            
        return render_template('logs.html', logs=logs, zones=zones,
                              selected_zone=selected_zone,
                              selected_kill=selected_kill,
                              show_duplicates=show_duplicates,
                              last_updated=get_last_updated('PS_version/csv_logs.csv'))
    except Exception as e:
        print(f"Error in logs route: {e}")
        import traceback
        traceback.print_exc()
        return render_template('logs.html', logs=[], zones=[],
                              selected_zone='',
                              selected_kill='',
                              show_duplicates=False,
                              last_updated=get_last_updated('PS_version/csv_logs.csv'))

@app.route('/fetch-logs', methods=['POST'])
def fetch_logs():
    try:
        if not SUDO_PASSWORD:
            print("Error: SUDO_PASSWORD environment variable not set")
            return jsonify({'error': 'Sudo password not configured'}), 500

        # Create the command (without sudo prefix since we're adding it to subprocess)
        command = "docker exec powershell-container pwsh -file PS_version/get_logs.ps1"
        command_args = command.split()

        print(f"Executing command: sudo {command}")

        # Execute the command with sudo password
        process = subprocess.Popen(['sudo', '-S'] + command_args,
                                 stdin=subprocess.PIPE,
                                 stdout=subprocess.PIPE,
                                 stderr=subprocess.PIPE,
                                 universal_newlines=True)

        # Send the password to stdin
        stdout, stderr = process.communicate(input=f"{SUDO_PASSWORD}\n")

        print(f"Command return code: {process.returncode}")
        print(f"Command stdout: {stdout}")
        print(f"Command stderr: {stderr}")

        if process.returncode != 0:
            print(f"Error executing command: {stderr}")
            return jsonify({'error': f'Failed to fetch logs: {stderr}'}), 500

        # Get current timestamp
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        return jsonify({
            'message': 'Logs fetched successfully',
            'timestamp': timestamp,
            'output': stdout
        })

    except Exception as e:
        print(f"Error in fetch_logs: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/progress')
def progress():
    try:
        # Get the selected week from query parameters, default to 'current'
        selected_week = request.args.get('week', 'current')
        
        # Check if files exist
        characters_path = 'PS_version/characters.csv'
        dungeon_details_path = 'PS_version/all_dungeon_details.csv'
        
        if not os.path.exists(characters_path):
            print(f"Error: Characters file not found at {os.path.abspath(characters_path)}")
            return render_template('progress.html', characters=[], weeks=[],
                                  last_updated=get_last_updated([characters_path, dungeon_details_path]),
                                  error=f"Characters file not found")
        
        if not os.path.exists(dungeon_details_path):
            print(f"Error: Dungeon details file not found at {os.path.abspath(dungeon_details_path)}")
            return render_template('progress.html', characters=[], weeks=[],
                                  last_updated=get_last_updated([characters_path, dungeon_details_path]),
                                  error=f"Dungeon details file not found")
        
        # Read the characters CSV
        characters_df = pd.read_csv(characters_path)
        
        # Read the dungeon details CSV
        dungeon_details_df = pd.read_csv(dungeon_details_path)
        
        # Convert date strings to datetime objects
        # Use format='mixed' to handle different date formats flexibly
        dungeon_details_df['Date'] = pd.to_datetime(dungeon_details_df['Date'], format='mixed', errors='coerce')
        
        # Calculate week number for each run
        dungeon_details_df['Week'] = dungeon_details_df['Date'].apply(get_week_number)
        
        # Get all available weeks
        all_weeks = sorted(dungeon_details_df['Week'].unique())  # Sort in ascending order
        
        # Determine which week to filter by
        filter_week = None
        if selected_week != 'current':
            filter_week = int(selected_week)
        else:
            # For current week, get the most recent week
            today = datetime.now()
            filter_week = get_week_number(today)
        
        # Process each character
        characters_data = []
        for _, character_row in characters_df.iterrows():
            character_name = character_row['name']
            
            # Filter runs for this character
            character_runs = dungeon_details_df[dungeon_details_df['Character'] == character_name]
            
            # Calculate total runs
            total_runs = len(character_runs)
            
            # Calculate runs for the selected week
            week_runs = character_runs[character_runs['Week'] == filter_week]
            week_runs_count = len(week_runs)
            
            # Find highest key level for the selected week only
            highest_key = 0
            if not week_runs.empty and 'Key Level' in week_runs.columns:
                highest_key = week_runs['Key Level'].max()
            
            characters_data.append({
                'name': character_name,
                'week_number': filter_week,
                'total_runs': total_runs,
                'this_week': week_runs_count,
                'highest': highest_key
            })
        
        # Pass the selected_week to the template
        return render_template('progress.html',
                              characters=characters_data,
                              weeks=all_weeks,
                              last_updated=get_last_updated([characters_path, dungeon_details_path]),
                              selected_week=selected_week)
    except Exception as e:
        print(f"Error: {e}")
        return render_template('progress.html', characters=[], weeks=[],
                              last_updated=get_last_updated(['PS_version/characters.csv', 'PS_version/all_dungeon_details.csv']))




if __name__ == '__main__':
    app.run(debug=True, port=5555, host='*************') 
