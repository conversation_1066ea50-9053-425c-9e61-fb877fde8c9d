{% extends "base.html" %}

{% block title %}Raider Management - Uproar{% endblock %}

{% block content %}
<!-- <PERSON> Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-1">
                    <i class="fas fa-users text-accent me-2"></i>
                    Raider Management
                </h1>
            </div>
            <div>
                <button id="updateDiscordIds" class="btn btn-outline-primary me-2">
                    <i class="fas fa-sync-alt me-2"></i>Update Discord IDs
                </button>
                <button id="updateButton" class="btn btn-primary">
                    <i class="fas fa-sync-alt me-2"></i>Refresh Data
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Add New Raider Form -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-user-plus me-2"></i>
                    Add New Raider
                </h6>
            </div>
            <div class="card-body">
                <form id="addUrlForm" class="row g-3">
                    <div class="col-lg-5 col-md-6">
                        <label for="newUrl" class="form-label">
                            <i class="fas fa-link me-1"></i>Armory URL
                        </label>
                        <input type="url" class="form-control" id="newUrl"
                               placeholder="https://worldofwarcraft.blizzard.com/en-us/character/..." required>
                        <div class="form-text">Full character armory URL from Battle.net</div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <label for="discordId" class="form-label">
                            <i class="fas fa-hashtag me-1"></i>Discord ID
                        </label>
                        <input type="text" class="form-control" id="discordId"
                               placeholder="123456789012345678" required>
                        <div class="form-text">18-digit Discord user ID</div>
                    </div>
                    <div class="col-lg-2 col-md-6">
                        <label class="form-label">&nbsp;</label>
                        <button type="submit" class="btn btn-success d-block w-100">
                            <i class="fas fa-plus me-2"></i>Add Raider
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Raiders Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    Current Raiders
                </h6>
                <div class="d-flex gap-2">
                    <input type="text" class="form-control form-control-sm search-input"
                           placeholder="Search raiders..."
                           data-target=".table-modern"
                           style="width: 200px;">
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-modern mb-0">
                        <thead>
                            <tr>
                                <th data-sort="status">
                                    <i class="fas fa-flag me-1"></i>Status
                                </th>
                                <th data-sort="armory">
                                    <i class="fas fa-link me-1"></i>WoW Armory
                                </th>
                                <th data-sort="discord">
                                    <i class="fas fa-user me-1"></i>Name
                                </th>
                                <th data-sort="role">
                                    <i class="fab fa-discord me-1"></i>Discord Role
                                </th>
                                <th>
                                    <i class="fas fa-cogs me-1"></i>Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for url, discord, status, role in armory_urls %}
                            <tr>
                                <td>
                                    {% if status.lower() == 'approved' %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-check me-1"></i>Approved
                                        </span>
                                    {% elif status.lower() == 'pending' %}
                                        <span class="badge bg-warning">
                                            <i class="fas fa-clock me-1"></i>Pending
                                        </span>
                                    {% elif status.lower() == 'inactive' %}
                                        <span class="badge bg-danger">
                                            <i class="fas fa-times me-1"></i>Inactive
                                        </span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ status }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url }}" target="_blank" class="text-primary text-decoration-none">
                                        <i class="fas fa-external-link-alt me-1"></i>
                                        View Armory
                                    </a>
                                </td>
                                <td>
                                    <span class="fw-bold">{{ discord if discord else 'Not provided' }}</span>
                                </td>
                                <td>
                                    {% if role.lower() == 'assigned' %}
                                        <span class="badge bg-success">
                                            <i class="fab fa-discord me-1"></i>Assigned
                                        </span>
                                    {% else %}
                                        <span class="badge bg-secondary">
                                            <i class="fab fa-discord me-1"></i>Not assigned
                                        </span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if status.lower() == 'approved' %}
                                        <button class="btn btn-danger btn-sm remove-url action-btn" data-url="{{ url }}">
                                            <i class="fas fa-user-times me-1"></i>Revoke
                                        </button>
                                    {% elif status.lower() == 'inactive' %}
                                        <button class="btn btn-success btn-sm reactivate-url action-btn" data-url="{{ url }}">
                                            <i class="fas fa-user-check me-1"></i>Activate
                                        </button>
                                    {% else %}
                                        <button class="btn btn-warning btn-sm approve-url action-btn" data-url="{{ url }}">
                                            <i class="fas fa-user-plus me-1"></i>Approve
                                        </button>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Update button functionality
    const updateButton = document.getElementById('updateButton');
    if (updateButton) {
        updateButton.addEventListener('click', async function() {
            try {
                updateButton.disabled = true;
                updateButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Updating...';

                const response = await fetch('/update_data', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const data = await response.json();

                if (response.ok) {
                    alert('Data update completed successfully');
                    location.reload();
                } else {
                    throw new Error(data.error || 'Failed to update data');
                }
            } catch (error) {
                console.error('Error:', error);
                alert(`Update failed: ${error.message}`);
            } finally {
                updateButton.disabled = false;
                updateButton.innerHTML = '<i class="fas fa-sync-alt me-2"></i>Refresh Data';
            }
        });
    }

    // Update Discord IDs button functionality
    const updateDiscordIdsButton = document.getElementById('updateDiscordIds');
    if (updateDiscordIdsButton) {
        updateDiscordIdsButton.addEventListener('click', async function() {
            try {
                updateDiscordIdsButton.disabled = true;
                updateDiscordIdsButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Updating...';

                const response = await fetch('/update_discord_ids', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const data = await response.json();

                if (response.ok) {
                    alert('Discord IDs updated successfully');
                    location.reload();
                } else {
                    throw new Error(data.error || 'Failed to update Discord IDs');
                }
            } catch (error) {
                console.error('Error:', error);
                alert(`Update failed: ${error.message}`);
            } finally {
                updateDiscordIdsButton.disabled = false;
                updateDiscordIdsButton.innerHTML = '<i class="fas fa-sync-alt me-2"></i>Update Discord IDs';
            }
        });
    }

    // Add URL form functionality
    const addUrlForm = document.getElementById('addUrlForm');
    if (addUrlForm) {
        addUrlForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            let newUrl = document.getElementById('newUrl').value;
            const discordId = document.getElementById('discordId').value;

            // Remove trailing slash if present
            newUrl = newUrl.replace(/\/$/, '');

            // Validate that the input is a valid Discord ID (numeric string)
            if (!/^\d+$/.test(discordId)) {
                alert('Invalid Discord ID. Please enter only numbers.');
                return;
            }

            // After getting a valid Discord ID, ask for Discord name
            const discordName = prompt('Nickname or raider name:');
            if (!discordName) {
                return;
            }

            try {
                const response = await fetch('/add_url', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        url: newUrl,
                        discord: discordName,
                        discord_id: discordId
                    })
                });

                if (response.ok) {
                    alert('Raider added successfully');
                    location.reload();
                } else {
                    const data = await response.json();
                    alert(data.error || 'Failed to add raider');
                }
            } catch (error) {
                console.error('Error:', error);
                alert('Failed to add raider');
            }
        });
    }

    // Approve URL buttons functionality
    document.querySelectorAll('.approve-url').forEach(button => {
        button.addEventListener('click', async function() {
            const url = this.dataset.url;
            if (confirm('Are you sure you want to approve this raider?')) {
                try {
                    const response = await fetch('/approve_url', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ url: url })
                    });

                    const data = await response.json();

                    if (response.ok) {
                        alert('Raider approved successfully');
                        location.reload();
                    } else {
                        const errorMessage = data.error || 'Failed to approve raider';
                        console.error('Error details:', data);
                        alert(`Error: ${errorMessage}`);
                    }
                } catch (error) {
                    console.error('Error:', error);
                    alert(`Error: ${error.message}`);
                }
            }
        });
    });

    // Remove URL buttons functionality
    document.querySelectorAll('.remove-url').forEach(button => {
        button.addEventListener('click', async function() {
            const url = this.dataset.url;
            if (confirm('Are you sure you want to revoke this raider?')) {
                try {
                    const response = await fetch('/remove_url', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ url: url })
                    });

                    if (response.ok) {
                        alert('Raider revoked successfully');
                        location.reload();
                    } else {
                        const data = await response.json();
                        alert(data.error || 'Failed to revoke raider');
                    }
                } catch (error) {
                    console.error('Error:', error);
                    alert('Failed to revoke raider');
                }
            }
        });
    });

    // Reactivate URL buttons functionality
    document.querySelectorAll('.reactivate-url').forEach(button => {
        button.addEventListener('click', async function() {
            const url = this.dataset.url;
            if (confirm('Are you sure you want to reactivate this raider?')) {
                try {
                    const response = await fetch('/reactivate_url', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ url: url })
                    });

                    if (response.ok) {
                        alert('Raider reactivated successfully');
                        location.reload();
                    } else {
                        const data = await response.json();
                        alert(data.error || 'Failed to reactivate raider');
                    }
                } catch (error) {
                    console.error('Error:', error);
                    alert('Failed to reactivate raider');
                }
            }
        });
    });

    // Add search functionality
    const searchInput = document.querySelector('.search-input');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const rows = document.querySelectorAll('.table-modern tbody tr');

            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
    }
});
</script>

<!-- Additional CSS for raiders-specific styling -->
<style>
.action-btn {
    min-width: 100px;
    text-align: center;
}
</style>
{% endblock %}